# Chat Module

A reusable, modular chat system that can be used throughout your project. The chat module provides a complete three-panel layout with user profiles, messaging, and dialog management.

## Features

- **Modular Components**: Break down complex chat UI into reusable components
- **Common User Panels**: Left and right panels use the same component with different data
- **Dialog Management**: Unified system for handling modals (Confirm, Hold, Problem)
- **TypeScript Support**: Full type safety with comprehensive interfaces
- **Configurable**: Pass objects to configure behavior and data

## Components

### Main Layout
- `ChatLayout` - Main three-column layout component
- `UserPanel` - Common component for left/right user panels
- `ChatPanel` - Center chat area with messages and input

### Individual Components
- `UserProfileCard` - User avatar, name, and details
- `AffiliateSelector` - Affiliate status dropdown
- `NotesSection` - Notes display and input
- `PersonalInfoSection` - Personal information cards
- `ChatHeader` - Search and statistics
- `MessagesList` - Message display with grouping
- `MessageItem` - Individual message component
- `ChatInput` - Message input with timer and actions

## Usage

```tsx
import { ChatLayout } from "@/components/chat";
import type { UserPanelProps, ChatPanelProps } from "@/components/chat";

function MyChatPage() {
  // Prepare your data
  const leftUser: UserPanelProps = {
    user: {
      id: "1",
      name: "User Name",
      avatar: "/avatar.png",
      fallback: "UN",
      // ... other user properties
    },
    notes: [/* your notes */],
    personalInfo: [/* your personal info */],
    onAddNote: (note) => console.log("Add note:", note),
    onAffiliateChange: (isAffiliate) => console.log("Affiliate:", isAffiliate)
  };

  const rightUser: UserPanelProps = {
    // Similar structure for right user
  };

  const chat: ChatPanelProps = {
    messages: [/* your messages */],
    stats: { total: 0, down: 8, up: 9 },
    onReplyStay: () => console.log("Reply & Stay"),
    onReply: () => console.log("Reply"),
    onLobby: () => console.log("Lobby"),
    onSendMessage: (message) => console.log("Send:", message)
  };

  return (
    <ChatLayout
      leftUser={leftUser}
      rightUser={rightUser}
      chat={chat}
      onConfirmAction={() => console.log("Confirm")}
      onHoldAction={() => console.log("Hold")}
      onProblemAction={() => console.log("Problem")}
    />
  );
}
```

## Data Interfaces

### UserProfile
```tsx
interface UserProfile {
  id: string;
  name: string;
  age?: number;
  avatar?: string;
  fallback: string;
  location?: string;
  vtl?: number;
  timezone?: string;
  lastActive?: string;
  relationStatus?: string;
  country?: string;
  interests?: string;
  isAffiliate?: boolean;
  onClick?: () => void;
}
```

### Message
```tsx
interface Message {
  id: string;
  content: string;
  timestamp: string; // ISO string or formatted display string
  isReceived: boolean;
  avatar?: string;
}
```

### Note
```tsx
interface Note {
  id: string;
  content: string;
  editedDate: string;
}
```

### PersonalInfo
```tsx
interface PersonalInfo {
  id: string;
  icon: React.ComponentType<any>;
  title: string;
  items: string[];
  onEdit?: () => void;
}
```

## Benefits

1. **Reusability**: Use the same chat module across different pages
2. **Consistency**: Common components ensure consistent UI/UX
3. **Maintainability**: Changes to chat functionality only need to be made in one place
4. **Type Safety**: Full TypeScript support prevents runtime errors
5. **Flexibility**: Configure behavior through props and callbacks
6. **Dialog Management**: Centralized modal handling with consistent behavior

## Example Implementation

See `src/features/members/profile/components/chat-profile-view.tsx` for a complete example of how to use the modular chat system.
