import { IconSearch, IconList, IconArrowDown, IconArrowUp } from "@tabler/icons-react";
import { ChatStats } from "./types";

interface ChatHeaderProps {
  stats: ChatStats;
  onHold?: () => void;
  onSearch?: (query: string) => void;
}

export function ChatHeader({ stats, onHold, onSearch }: ChatHeaderProps) {
  return (
    <div className="flex items-center gap-3 p-4 bg-[#171717] border-b rounded-t-2xl">
      {/* Search Input */}
      <div className="flex items-center bg-[#444] px-3 py-2 rounded-md text-white w-full max-w-md">
        <IconSearch size={16} className="mr-2 text-gray-300" />
        <input
          type="text"
          placeholder="Search Keyword"
          className="bg-transparent text-sm text-white placeholder-gray-300 outline-none w-full"
          onChange={(e) => onSearch?.(e.target.value)}
        />
      </div>

      {/* Stat Box */}
      <div className="flex items-center bg-[#252525] text-white text-sm px-4 py-2 rounded-xs gap-3">
        <span className="flex items-center gap-1">
          {String(stats.total).padStart(2, '0')} <IconList size={14} />
        </span>
        <div className="border-l border-gray-400 h-4" />
        <span className="flex items-center gap-1">
          {String(stats.down).padStart(2, '0')} <IconArrowDown size={14} />
        </span>
        <div className="border-l border-gray-400 h-4" />
        <span className="flex items-center gap-1">
          {String(stats.up).padStart(2, '0')} <IconArrowUp size={14} />
        </span>
      </div>

      {/* Hold Button */}
      <button 
        onClick={onHold}
        className="bg-white text-gray-700 text-sm px-4 py-2 rounded-xs cursor-pointer"
      >
        Hold
      </button>
    </div>
  );
}
