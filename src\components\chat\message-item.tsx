import UserImg from '@/assets/user.png';
import { Message } from "./types";

interface MessageItemProps {
  message: Message;
}

export function MessageItem({ message }: MessageItemProps) {
  if (message.isReceived) {
    return (
      <div className="Received-msg flex gap-2 w-full max-w-[85%]">
        <img 
          src={message.avatar || UserImg} 
          alt="user" 
          className="w-[40px] h-[40px] rounded-full" 
        />
        <div className="flex flex-col gap-2">
          <div className="w-fit break-words px-4 py-3 rounded-xl rounded-ss-none bg-sidebar-accent text-sm">
            {message.content}
          </div>
          <div className="text-xs text-gray-500">{message.timestamp}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="sent-msg flex justify-end gap-2 w-full ms-auto max-w-[85%]">
      <div className="flex flex-col items-end gap-2">
        <div className="w-fit break-words px-4 py-3 rounded-xl rounded-se-none bg-sidebar-primary text-sm text-white">
          {message.content}
        </div>
        <div className="text-xs text-gray-500 text-end">{message.timestamp}</div>
      </div>
      <img 
        src={message.avatar || UserImg} 
        alt="user" 
        className="w-[40px] h-[40px] rounded-full" 
      />
    </div>
  );
}
