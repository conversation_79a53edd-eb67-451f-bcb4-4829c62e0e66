import { MessageItem } from "./message-item";
import { Message } from "./types";

interface MessagesListProps {
  messages: Message[];
}

export function MessagesList({ messages }: MessagesListProps) {
  // Group messages by date
  const groupedMessages = messages.reduce((groups, message) => {
    const date = new Date(message.timestamp).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(message);
    return groups;
  }, {} as Record<string, Message[]>);

  return (
    <div className="flex-1 bg-sidebar p-4 space-y-4 overflow-y-auto max-h-[calc(100dvh-350px)]">
      {Object.entries(groupedMessages).map(([date, dayMessages]) => (
        <div key={date}>
          {dayMessages.map((message, index) => (
            <div key={message.id} className="mb-4">
              <MessageItem message={message} />
            </div>
          ))}
          
          {/* Date separator */}
          <div className="text-xs text-gray-600 bg-sidebar text-center mt-4 w-fit px-4 py-2 rounded-full mx-auto">
            {new Date(date).toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'short', 
              day: 'numeric' 
            })}
          </div>
        </div>
      ))}
    </div>
  );
}
