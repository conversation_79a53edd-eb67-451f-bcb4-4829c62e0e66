export interface UserProfile {
  id: string;
  name: string;
  age?: number;
  avatar?: string;
  fallback: string;
  location?: string;
  vtl?: number;
  timezone?: string;
  lastActive?: string;
  relationStatus?: string;
  country?: string;
  interests?: string;
  isAffiliate?: boolean;
  onClick?: () => void;
}

export interface Note {
  id: string;
  content: string;
  editedDate: string;
}

export interface PersonalInfo {
  id: string;
  icon: React.ComponentType<any>;
  title: string;
  items: string[];
  onEdit?: () => void;
}

export interface Message {
  id: string;
  content: string;
  timestamp: string;
  isReceived: boolean;
  avatar?: string;
}

export interface ChatStats {
  total: number;
  down: number;
  up: number;
}

export interface ChatPanelProps {
  messages: Message[];
  stats: ChatStats;
  onHold?: () => void;
  onReplyStay?: () => void;
  onReply?: () => void;
  onLobby?: () => void;
  onProblem?: () => void;
  onSendMessage?: (message: string) => void;
}

export interface UserPanelProps {
  user: UserProfile;
  notes: Note[];
  personalInfo: PersonalInfo[];
  onAddNote?: (note: string) => void;
  onAffiliateChange?: (isAffiliate: boolean) => void;
}
