import { Avatar, AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";
import { UserProfile } from "./types";

interface UserProfileCardProps {
  user: UserProfile;
}

export function UserProfileCard({ user }: UserProfileCardProps) {
  return (
    <div 
      className="bg-sidebar p-4 rounded-2xl cursor-pointer" 
      onClick={user.onClick}
    >
      <Avatar className="w-[54px] h-[54px] mb-[12px] rounded-full bg-[#999] text-white flex items-center justify-center">
        <AvatarImage src={user.avatar} alt={user.name} />
        <AvatarFallback className="text-lg">{user.fallback}</AvatarFallback>
      </Avatar>
      
      <div className="text-lg font-medium">
        {user.name} {user.age && `(${user.age})`}
      </div>
      
      {user.location && user.vtl !== undefined && (
        <div className="text-sm">{user.location} - VTL: {user.vtl}</div>
      )}
      
      {user.lastActive && (
        <div className="text-sm">Last Active: {user.lastActive}</div>
      )}
      
      {user.timezone && (
        <div className="text-sm">Timezone: {user.timezone}</div>
      )}
      
      <hr className="my-3" />
      
      <div className="flex flex-wrap">
        {user.relationStatus && (
          <div className="basis-1/2">
            <div className="text-sm">Relation Status</div>
            <div className="text-sm font-semibold">{user.relationStatus}</div>
          </div>
        )}
        
        {user.country && (
          <div className="basis-1/2">
            <div className="text-sm">Location</div>
            <div className="text-sm font-semibold">{user.country}</div>
          </div>
        )}
        
        {user.interests && (
          <div className="basis-2/2 mt-3">
            <div className="text-sm">Interests</div>
            <div className="text-sm font-semibold">{user.interests}</div>
          </div>
        )}
      </div>
    </div>
  );
}
