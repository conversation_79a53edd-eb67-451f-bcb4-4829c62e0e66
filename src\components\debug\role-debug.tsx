/**
 * Debug component for testing role-based navigation
 * This component can be temporarily added to any page to test role functionality
 */

import React, { useState } from 'react';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { testRoleAccess, TEST_ROLES, getRoleSummary } from '@/utils/role-testing';

export function RoleDebugPanel() {
  const { user, roles, setUser } = useAuthStore((state) => state.auth);
  const [selectedTestRole, setSelectedTestRole] = useState<string>('ADMIN');
  const [testResults, setTestResults] = useState<any>(null);

  const currentRoles = React.useMemo(() => {
    if (roles && Array.isArray(roles)) {
      return roles;
    }
    if (user?.role && Array.isArray(user.role)) {
      return user.role;
    }
    return [];
  }, [user, roles]);

  const handleTestRole = (roleKey: string) => {
    const testRoles = TEST_ROLES[roleKey as keyof typeof TEST_ROLES];
    const results = testRoleAccess(testRoles);
    setTestResults({ roleKey, results });
  };

  const handleSetUserRole = (roleKey: string) => {
    const testRoles = TEST_ROLES[roleKey as keyof typeof TEST_ROLES];
    // Temporarily set user roles for testing
    setUser({ ...user, role: testRoles }, testRoles);
  };

  const summary = getRoleSummary();

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md">
      <Card className="shadow-lg">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center justify-between">
            🔐 Role Debug Panel
            <Badge variant="outline" className="text-xs">
              Current: {currentRoles.join(', ') || 'None'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Current User Info */}
          <div className="text-xs space-y-1">
            <div><strong>User:</strong> {user?.email || 'Not logged in'}</div>
            <div><strong>Roles:</strong> {currentRoles.join(', ') || 'None'}</div>
          </div>

          {/* Test Role Buttons */}
          <div className="space-y-2">
            <div className="text-xs font-medium">Test Navigation with Role:</div>
            <div className="grid grid-cols-2 gap-1">
              {Object.keys(TEST_ROLES).map((roleKey) => (
                <Button
                  key={roleKey}
                  variant="outline"
                  size="sm"
                  className="text-xs h-7"
                  onClick={() => handleTestRole(roleKey)}
                >
                  {roleKey.replace('_', ' ')}
                </Button>
              ))}
            </div>
          </div>

          {/* Set User Role for Testing */}
          <div className="space-y-2">
            <div className="text-xs font-medium">Set Current User Role:</div>
            <div className="grid grid-cols-2 gap-1">
              {Object.keys(TEST_ROLES).map((roleKey) => (
                <Button
                  key={roleKey}
                  variant={currentRoles.join(',') === TEST_ROLES[roleKey as keyof typeof TEST_ROLES].join(',') ? 'default' : 'secondary'}
                  size="sm"
                  className="text-xs h-7"
                  onClick={() => handleSetUserRole(roleKey)}
                >
                  {roleKey.replace('_', ' ')}
                </Button>
              ))}
            </div>
          </div>

          {/* Test Results */}
          {testResults && (
            <div className="space-y-2 border-t pt-2">
              <div className="text-xs font-medium">
                Results for {testResults.roleKey}:
              </div>
              <div className="text-xs space-y-1 max-h-32 overflow-y-auto">
                <div>Groups: {testResults.results.totalGroups}</div>
                {testResults.results.groups.map((group: any, idx: number) => (
                  <div key={idx} className="pl-2">
                    <div className="font-medium">{group.title || 'Main'}</div>
                    <div className="pl-2 text-gray-600">
                      {group.items.map((item: any, itemIdx: number) => (
                        <div key={itemIdx}>
                          • {item.title} {item.subItemCount > 0 && `(${item.subItemCount})`}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Role Summary */}
          <div className="space-y-2 border-t pt-2">
            <div className="text-xs font-medium">Role Access Summary:</div>
            <div className="text-xs space-y-1 max-h-24 overflow-y-auto">
              {summary.map((item, idx) => (
                <div key={idx} className="flex justify-between">
                  <span>{item.roleName}:</span>
                  <span>{item.totalAccessibleItems} items</span>
                </div>
              ))}
            </div>
          </div>

          {/* Console Log Button */}
          <Button
            variant="outline"
            size="sm"
            className="w-full text-xs"
            onClick={() => {
              console.log('🔐 Current Role Access:', testRoleAccess(currentRoles));
              console.log('📊 All Role Summary:', summary);
            }}
          >
            Log to Console
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

// Conditional export for development only
export const RoleDebug = process.env.NODE_ENV === 'development' ? RoleDebugPanel : () => null;
