import { END_POINTS } from "@/features/members/utils/constant";
import { ActivityLogIcon, SpeakerLoudIcon } from "@radix-ui/react-icons";
import {
  IconAffiliate,
  IconChartInfographic,
  IconLayoutDashboard,
  IconMessages,
  IconPalette,
  IconSettings,
  IconUserCog,
} from "@tabler/icons-react";
import { url } from "inspector";
import {
  AudioWaveform, BadgeDollarSign, Bot, ChartBarIncreasing,
  Command, Contact, DollarSign, FolderOpen, GalleryVerticalEnd, Gift, Globe, Image,
  HeartHandshake, LanguagesIcon, List, MessageSquareIcon,
  Package,
  PersonStandingIcon,
  Phone,
  Plus, ShieldMinus, ShieldPlus, Smile, Sofa, SofaIcon, UserCheck2Icon, UserCog, Users,
  Users2
} from "lucide-react";

export const sidebarData: any = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
    {
      name: "Redsoft Admin",
      logo: Command,
      plan: "",
    },
    // {
    //   name: "Acme Inc",
    //   logo: GalleryVerticalEnd,
    //   plan: "Enterprise",
    // },
    // {
    //   name: "Acme Corp.",
    //   logo: AudioWaveform,
    //   plan: "Startup",
    // },
  ],
  navGroups: [
    {
      title: "",
      items: [
        {
          title: "Dashboard",
          url: "/",
          icon: IconLayoutDashboard,
        },
        {
          title: "Sessions",
          icon: Users2,
          roles: ['chat-mod'],
          items: [
            {
              title: "Loby",
              icon: Sofa,
              url: `${END_POINTS.LOBY}`
            }, {
              title: "Active Sessions",
              url: "/active-sessions",
              icon: PersonStandingIcon,
            },
          ]
        },
        {
          title: "Settings",
          icon: IconSettings,
          roles: ['superadmin'],
          url: "/settings"
        }, {
          title: "Currencies",
          url: "/currencies",
          icon: DollarSign,
        }, {
          title: "Announcements",
          url: "/announcements",
          icon: SpeakerLoudIcon,
        },
        {
          title: "Admin Login Activity",
          url: "/admin-login-activity",
          icon: ActivityLogIcon,
        },
        {
          title: "User-Management",
          icon: Users,
          items: [
            {
              title: "Moderators",
              icon: IconSettings,
              items: [
                {
                  title: "List",
                  url: "/moderators",
                  icon: IconUserCog,
                },
                {
                  title: "All Reply Messages",
                  url: "/moderators/reply-messages",
                  icon: IconMessages,
                },
                // {
                //   title: "Quality Analysis",
                //   url: "/settings/appearance",
                //   icon: IconPalette,
                // },
                {
                  title: "Login Activity",
                  url: "/moderators/login-activity",
                  icon: ActivityLogIcon,
                },
                {
                  title: "Domains",
                  url: "/moderators/domain",
                  icon: Globe,
                },
              ],
            },

            {
              title: "Members",
              icon: IconSettings,
              items: [
                {
                  title: "List",
                  url: "/members",
                  icon: IconUserCog,
                },
                {
                  title: "Profile",
                  // url: "/members/profile",
                  icon: IconPalette,
                },
              ],
            },
            {
              title: "Models",
              icon: IconSettings,
              items: [
                {
                  title: "List",
                  url: "/models",
                  icon: IconUserCog,
                },
                {
                  title: "Profile",
                  // url: "/models/profile",
                  icon: IconPalette,
                },
              ],
            },
          ],
        },
        {
          title: "Domains",
          icon: Globe,
          items: [
            {
              title: "Waiting Approval List",
              icon: ShieldMinus,
            },

            {
              title: "Active Domain List",
              icon: ShieldPlus,

            },
          ],
        },
        {
          title: "Country Languages",
          icon: LanguagesIcon,
          items: [
            {
              title: "List",
              icon: List,
            },

            {
              title: "Add",
              icon: Plus,

            },
          ],
        },
        {
          title: "Affiliates",
          icon: IconAffiliate,
          items: [
            {
              title: "New Register",
              icon: UserCog,
            },

            {
              title: "Approved Affiliates",
              icon: IconSettings,

            },
            {
              title: "Add",
              icon: Plus,

            },
            {
              title: "Assign Customer",
              icon: UserCheck2Icon,

            },
          ],
        },
        {
          title: "Affiliate Offers",
          icon: IconAffiliate,
          items: [
            {
              title: "List",
              icon: List,
            },

            {
              title: "Add",
              icon: Plus,

            },
          ],
        },
        {
          title: "Statistics",
          icon: IconChartInfographic,
          items: [
            {
              title: "Messages",
              icon: IconMessages,
            },

            {
              title: "Sales",
              icon: BadgeDollarSign,

            },
            {
              title: "Sales Chart",
              icon: ChartBarIncreasing,

            },
            {
              title: "WL Sales",
              icon: ChartBarIncreasing,

            },
          ],
        },
        {
          title: "Packages",
          icon: Package,
          // url: END_POINTS.PACKAGES,
        },
        {
          title: "Resources",
          icon: FolderOpen,
          items: [
            {
              title: "Smilies",
              icon: Smile,
              url: END_POINTS.SMILIES,
            },
            // {
            //   title: "GIFs",
            //   icon: Image,
            //   url: END_POINTS.GIFS,
            // },
            {
              title: "Gifts",
              icon: Gift,
              url: END_POINTS.GIFTS,
            },
          ],
        },
        {
          title: "Flirt Messages",
          icon: HeartHandshake,
          url: END_POINTS.FLIRT_MESSAGES,
        },
        {
          title: "Bot Messages",
          icon: Bot,
          url: END_POINTS.BOT_MESSAGES,
          // items: [
          //   {
          //     title: "List",
          //     icon: List,
          //   },

          //   {
          //     title: "Add",
          //     icon: Plus,
          //   },
          // ],
        },
        {
          title: "Contact us",
          icon: Contact,

          url: "/contact-us",
        },
      ],
    },
  ],
};
