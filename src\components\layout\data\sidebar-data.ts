import { END_POINTS } from "@/features/members/utils/constant";
import { ActivityLogIcon, SpeakerLoudIcon } from "@radix-ui/react-icons";
import {
  IconAffiliate,
  IconChartInfographic,
  IconLayoutDashboard,
  IconMessages,
  IconPalette,
  IconSettings,
  IconUserCog,
} from "@tabler/icons-react";
import { url } from "inspector";
import {
  AudioWaveform, BadgeDollarSign, Bot, ChartBarIncreasing,
  Command, Contact, DollarSign, FolderOpen, GalleryVerticalEnd, Gift, Globe, Image,
  HeartHandshake, LanguagesIcon, List, MessageSquareIcon,
  Package,
  PersonStandingIcon,
  Phone,
  Plus, ShieldMinus, ShieldPlus, Smile, Sofa, SofaIcon, UserCheck2Icon, UserCog, Users,
  Users2
} from "lucide-react";

export const sidebarData: any = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
    {
      name: "Redsoft Admin",
      logo: Command,
      plan: "",
    },
    // {
    //   name: "Acme Inc",
    //   logo: GalleryVerticalEnd,
    //   plan: "Enterprise",
    // },
    // {
    //   name: "Acme Corp.",
    //   logo: AudioWaveform,
    //   plan: "Startup",
    // },
  ],
  navGroups: [
    {
      title: "",
      items: [
        {
          title: "Dashboard",
          url: "/",
          icon: IconLayoutDashboard,
        },
        {
          title: "Sessions",
          icon: Users2,
          roles: ['superadmin', 'admin', 'manager', 'chat-mod'],
          items: [
            {
              title: "Loby",
              icon: Sofa,
              url: `${END_POINTS.LOBY}`,
              roles: ['superadmin', 'admin', 'manager', 'chat-mod']
            }, {
              title: "Active Sessions",
              url: "/active-sessions",
              icon: PersonStandingIcon,
              roles: ['superadmin', 'admin', 'manager', 'chat-mod']
            },
          ]
        },
        {
          title: "Settings",
          icon: IconSettings,
          roles: ['superadmin', 'admin'],
          url: "/settings"
        }, {
          title: "Currencies",
          url: "/currencies",
          icon: DollarSign,
          roles: ['superadmin', 'admin', 'manager']
        }, {
          title: "Announcements",
          url: "/announcements",
          icon: SpeakerLoudIcon,
          roles: ['superadmin', 'admin', 'manager']
        },
        {
          title: "Admin Login Activity",
          url: "/admin-login-activity",
          icon: ActivityLogIcon,
          roles: ['superadmin', 'admin']
        },
        {
          title: "User-Management",
          icon: Users,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "Moderators",
              icon: IconSettings,
              roles: ['superadmin', 'admin'],
              items: [
                {
                  title: "List",
                  url: "/moderators",
                  icon: IconUserCog,
                  roles: ['superadmin', 'admin']
                },
                {
                  title: "All Reply Messages",
                  url: "/moderators/reply-messages",
                  icon: IconMessages,
                  roles: ['superadmin', 'admin']
                },
                // {
                //   title: "Quality Analysis",
                //   url: "/settings/appearance",
                //   icon: IconPalette,
                // },
                {
                  title: "Login Activity",
                  url: "/moderators/login-activity",
                  icon: ActivityLogIcon,
                  roles: ['superadmin', 'admin']
                },
                {
                  title: "Domains",
                  url: "/moderators/domain",
                  icon: Globe,
                  roles: ['superadmin', 'admin']
                },
              ],
            },

            {
              title: "Members",
              icon: IconSettings,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/members",
                  icon: IconUserCog,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Profile",
                  // url: "/members/profile",
                  icon: IconPalette,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Models",
              icon: IconSettings,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/models",
                  icon: IconUserCog,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Profile",
                  // url: "/models/profile",
                  icon: IconPalette,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
          ],
        },
        {
          title: "Domains",
          icon: Globe,
          roles: ['superadmin', 'admin'],
          items: [
            {
              title: "Waiting Approval List",
              icon: ShieldMinus,
              roles: ['superadmin', 'admin']
            },

            {
              title: "Active Domain List",
              icon: ShieldPlus,
              roles: ['superadmin', 'admin']
            },
          ],
        },
        {
          title: "Country Languages",
          icon: LanguagesIcon,
          roles: ['superadmin', 'admin'],
          items: [
            {
              title: "List",
              icon: List,
              roles: ['superadmin', 'admin']
            },

            {
              title: "Add",
              icon: Plus,
              roles: ['superadmin', 'admin']
            },
          ],
        },
        {
          title: "Affiliates",
          icon: IconAffiliate,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "New Register",
              icon: UserCog,
              roles: ['superadmin', 'admin', 'manager']
            },

            {
              title: "Approved Affiliates",
              icon: IconSettings,
              roles: ['superadmin', 'admin', 'manager']
            },
            {
              title: "Add",
              icon: Plus,
              roles: ['superadmin', 'admin']
            },
            {
              title: "Assign Customer",
              icon: UserCheck2Icon,
              roles: ['superadmin', 'admin', 'manager']
            },
          ],
        },
        {
          title: "Affiliate Offers",
          icon: IconAffiliate,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "List",
              icon: List,
              roles: ['superadmin', 'admin', 'manager']
            },

            {
              title: "Add",
              icon: Plus,
              roles: ['superadmin', 'admin']
            },
          ],
        },
        {
          title: "Statistics",
          icon: IconChartInfographic,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "Messages",
              icon: IconMessages,
              roles: ['superadmin', 'admin', 'manager']
            },

            {
              title: "Sales",
              icon: BadgeDollarSign,
              roles: ['superadmin', 'admin', 'manager']
            },
            {
              title: "Sales Chart",
              icon: ChartBarIncreasing,
              roles: ['superadmin', 'admin', 'manager']
            },
            {
              title: "WL Sales",
              icon: ChartBarIncreasing,
              roles: ['superadmin', 'admin', 'manager']
            },
          ],
        },
        {
          title: "Packages",
          icon: Package,
          roles: ['superadmin', 'admin', 'manager'],
          // url: END_POINTS.PACKAGES,
        },
        {
          title: "Resources",
          icon: FolderOpen,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "Smilies",
              icon: Smile,
              url: END_POINTS.SMILIES,
              roles: ['superadmin', 'admin', 'manager']
            },
            {
              title: "GIFs",
              icon: Image,
              url: END_POINTS.GIFS,
              roles: ['superadmin', 'admin', 'manager']
            },
            {
              title: "Gifts",
              icon: Gift,
              url: END_POINTS.GIFTS,
              roles: ['superadmin', 'admin', 'manager']
            },
          ],
        },
        {
          title: "Flirt Messages",
          icon: HeartHandshake,
          url: END_POINTS.FLIRT_MESSAGES,
          roles: ['superadmin', 'admin', 'manager']
        },
        {
          title: "Bot Messages",
          icon: Bot,
          url: END_POINTS.BOT_MESSAGES,
          roles: ['superadmin', 'admin', 'manager']
          // items: [
          //   {
          //     title: "List",
          //     icon: List,
          //   },

          //   {
          //     title: "Add",
          //     icon: Plus,
          //   },
          // ],
        },
        {
          title: "Contact us",
          icon: Contact,
          url: "/contact-us",
          roles: ['superadmin', 'admin', 'manager']
        },
      ],
    },
  ],
};
