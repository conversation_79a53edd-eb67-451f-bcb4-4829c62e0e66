import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useState } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { Input } from '@/components/ui/input'
import { CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  readonly onFilterChanged?: any
}

const adminTypeOptions = [
  'Admin',
  'Super Admin',
  'Moderator',
]

export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const [filters, setFilters] = useState({
    search: "",
    role: undefined as string | undefined,
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
  })

  const [startDateOpen, setStartDateOpen] = useState(false)
  const [endDateOpen, setEndDateOpen] = useState(false)

  const [hasSearched, setHasSearched] = useState(false)

  const handleFilterChange = (
    key: 'role' | 'startDate' | 'endDate' | 'search',
    value: any
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  // Check if both dates are selected (treat as single entity)
  const hasCompleteDateRange = filters.startDate && filters.endDate

  // Check if other filters are active (excluding incomplete date range)
  const hasOtherFilters = filters.role || filters.search

  // Only consider dates if both are selected
  const hasActiveFilters = hasOtherFilters || hasCompleteDateRange

  const handleSearch = () => {
    const searchFilters: any = {};
    if (filters.search) searchFilters.search = filters.search;
    if (filters.role) searchFilters.role = filters.role;
    if (hasCompleteDateRange) {
      searchFilters.startDate = filters.startDate;
      searchFilters.endDate = filters.endDate;
    }
    onFilterChanged(searchFilters, 1);
    setHasSearched(true);
  }

  const handleReset = () => {
    // Reset all filters

    const filterValue: any = {
      role: undefined,
      search: ""
    }
    setFilters(filterValue)

    onFilterChanged(filterValue, 0)
    setHasSearched(false)
  }

  return (
    <div className="flex items-center gap-3 w-full py-2 flex-wrap">
      {/* Example: If you have a text search, add it here */}
      {/* <Input placeholder="Search by ..." className="h-9 w-[220px]" /> */}
      <Input
        placeholder='Search by username...'
        value={filters.search}
        onChange={(event) => handleFilterChange('search', event.target.value)}
        className='h-8 w-[250px]'
      />
      <FilterSelect
        value={filters.role}
        placeholder="Admin Type"
        options={adminTypeOptions}
        onChange={(value) => handleFilterChange('role', value)}
        className="h-9 min-w-[160px] bg-card"
      />
      <div className="flex gap-2">
        <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
          <PopoverTrigger asChild>
            <div className="relative w-[160px] bg-card">
              <Input
                readOnly
                value={filters.startDate ? format(filters.startDate, "yyyy-MM-dd") : ""}
                placeholder="From Date"
                className="pr-10 cursor-pointer h-9"
                onClick={() => setStartDateOpen(true)}
              />
              <CalendarIcon
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                width={20}
                height={20}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="relative">
              <Calendar
                mode="single"
                selected={filters.startDate}
                className="rounded-md border shadow-sm"
                captionLayout="dropdown"
                disabled={(date) => filters.endDate ? date > filters.endDate : false}
                onSelect={(date) => {
                  if (date) {
                    handleFilterChange('startDate', date);
                    setStartDateOpen(false);
                  }
                }}
              />
            </div>
          </PopoverContent>
        </Popover>
        <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
          <PopoverTrigger asChild>
            <div className="relative w-[160px] bg-card">
              <Input
                readOnly
                value={filters.endDate ? format(filters.endDate, "yyyy-MM-dd") : ""}
                placeholder="To Date"
                className="pr-10 cursor-pointer h-9"
                onClick={() => setEndDateOpen(true)}
              />
              <CalendarIcon
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                width={20}
                height={20}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="relative">
              <Calendar
                mode="single"
                selected={filters.endDate}
                className="rounded-md border shadow-sm"
                captionLayout="dropdown"
                disabled={(date) => filters.startDate ? date < filters.startDate : false}
                onSelect={(date) => {
                  if (date) {
                    handleFilterChange('endDate', date);
                    setEndDateOpen(false);
                  }
                }}
              />
            </div>
          </PopoverContent>
        </Popover>
      </div>
      <Button
        onClick={handleSearch}
        disabled={!hasActiveFilters}
        className="h-8 px-3"
        type="button"
      >
        Search
      </Button>
      {hasSearched && (
        <Button
          variant="outline"
          onClick={handleReset}
          className="h-8 px-3"
        >
          Reset
        </Button>
      )}
    </div>
  )
}
