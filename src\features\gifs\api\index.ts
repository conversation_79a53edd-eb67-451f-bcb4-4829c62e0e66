import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


export const getGifApi = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GIFS, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["gif-list"],
    });


export const addGifApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.GIFS, payload);
        },
    });


export const updateGifApi = () =>
    useMutation({
        mutationFn: async ({ id, ...payload }: any) => {
            return await apiClient.put(`${API_ENDPOINTS.GIFS}/${id}`, payload);
        },
    });

export const getGifDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.GIFS}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}

        },
        queryKey: ["gif-details", id],
        enabled: !!id
    });

export const deleteGifApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.delete(`${API_ENDPOINTS.GIFS}/${payload?.id}`);
        },
    });
