import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useState } from 'react'
import { preferenceValue, typeValue } from '../utils/constant'

interface DataTableToolbarProps<TData> {
    readonly table: Table<TData>
    readonly onFilterChanged?: any
}

const typeOptions = [
    'Regular Member',
    'New Member',
]

const preferenceOptions = [
    'For Male',
    'For Female',
]
export function DataTableToolbar<TData>({
    table,
    onFilterChanged
}: DataTableToolbarProps<TData>) {
    const isFiltered = table.getState().columnFilters.length > 0

    const [filters, setFilters] = useState({
        type: undefined as string | undefined,
        preference: undefined as string | undefined,
    })

    const [hasSearched, setHasSearched] = useState(false)

    const handleFilterChange = (
        key: 'type' | 'preference',
        value: string | undefined
    ) => {
        setFilters((prev) => ({ ...prev, [key]: value }))
    }

    const handleSearch = () => {
        // Apply filters to the table
        if (filters.type && (filters.type === 'Regular Member' || filters.type === 'New Member')) {
            const val = typeValue[filters.type]
            table.getColumn('type')?.setFilterValue(val)
        }
        if (filters.preference && (filters.preference === 'For Male' || filters.preference === 'For Female')) {
            const val = preferenceValue[filters.preference]
            console.log(val)
            table.getColumn('preference')?.setFilterValue(val)
        }

        setHasSearched(true)
        onFilterChanged?.({
            type: filters.type ? typeValue[filters.type as keyof typeof typeValue] : undefined,
            preference: filters.preference ? preferenceValue[filters.preference as keyof typeof preferenceValue] : undefined
        }, 1)
    }

    const handleReset = () => {
        // Reset all column filters
        table.resetColumnFilters()

        // Reset the custom filter function for profile column
        const profileColumn = table.getColumn('profile')
        if (profileColumn?.columnDef) {
            // Reset to default filter function (contains text search)
            profileColumn.columnDef.filterFn = 'includesString'
        }

        const f: any = {
            type: undefined,
            preference: undefined,
        }
        // Reset filter state
        setFilters(f)
        setHasSearched(false)
        onFilterChanged(f, 0)
    }

    const hasActiveFilters = Boolean(filters.type ?? filters.preference)

    return (
        <div className='flex items-center justify-between'>
            <div className='flex flex-1 items-center gap-4 flex-wrap'>
                <FilterSelect
                    value={filters.type}
                    placeholder="Select Type"
                    options={typeOptions}
                    onChange={(value) => handleFilterChange('type', value)}
                />
                <FilterSelect
                    value={filters.preference}
                    placeholder="Select Preference"
                    options={preferenceOptions}
                    onChange={(value) => handleFilterChange('preference', value)}
                />

                <Button
                    onClick={handleSearch}
                    className="h-8 px-3"
                    disabled={!hasActiveFilters}
                >
                    Search
                </Button>
                {(isFiltered || hasSearched) && (
                    <Button
                        variant='outline'
                        onClick={handleReset}
                        className='h-8 px-2 lg:px-3'
                    >
                        Reset
                        {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
                    </Button>
                )}
            </div>
        </div>
    )
}
