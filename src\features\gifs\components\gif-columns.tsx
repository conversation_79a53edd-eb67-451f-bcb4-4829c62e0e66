import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import LongText from '@/components/long-text'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { S3_BASE_URL } from '@/features/members/utils/utilities'

export const columns: ColumnDef<any>[] = [
    {
        accessorKey: 'serialNumber',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='#' />
        ),
        cell: ({ row }) => (
            <div className='w-8'>{row.getValue('serialNumber')}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
                'w-8'
            ),
        },
        enableHiding: false,
    },
    {
        accessorKey: 'image',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='GIF Image' />
        ),
        cell: ({ row }) => {
            const image = row?.getValue('image') as string

            return (
                <div className='flex items-center gap-3'>
                    <Avatar className='h-8 w-8'>
                        <AvatarImage src={S3_BASE_URL + row?.original?.image} alt={image} />
                        <AvatarFallback className='text-xs bg-gray-200'>
                        </AvatarFallback>
                    </Avatar>
                </div>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'code',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='GIF Code' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.getValue('code') || "N/A"}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'type',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='GIF Type' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.getValue('type') || "N/A"}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        id: 'actions',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Action' />
        ),
        enableSorting: false,
        cell: DataTableRowActions,
        meta: { className: 'w-16' },
    },
]
