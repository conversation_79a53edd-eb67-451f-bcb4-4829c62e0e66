import { Main } from "@/components/layout/main";
import MembersProvider from "./context/members-context";
import { MembersTable } from "./components/members-table";
import { columns } from "./components/members-columns";
import { useAuthStore } from "@/stores/authStore";

export default function List() {
  const { auth: user } = useAuthStore((state) => state)
  console.log('user is ', user)
  return (
    <MembersProvider>
      <Main>
        <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Members List
            </h2>
          </div>
        </div>
        <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
          <MembersTable columns={columns} />
        </div>
      </Main>
    </MembersProvider>
  );
}
