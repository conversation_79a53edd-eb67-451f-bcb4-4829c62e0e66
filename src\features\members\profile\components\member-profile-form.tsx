import { useEffect, useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { ChevronDownIcon, ChevronUpIcon } from '@radix-ui/react-icons'
import { useNavigate, useParams } from '@tanstack/react-router'
import { getCountryList, getMasterApi, getMemberDetails, updateMemberlApi } from '../../api'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form'
import { toast } from 'sonner'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { CalendarIcon } from '@radix-ui/react-icons'
import { format } from 'date-fns'
import { END_POINTS } from '../../utils/constant'
import { useQueryClient } from '@tanstack/react-query'

interface MemberProfileFormProps {
  member: any
}

const memberProfileSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  email: z.string().min(1, 'Email is required'),
  gender: z.number().min(1, 'Gender is required'),
  seekingFor: z.number().min(1, 'Seeking For is required'),
  dob: z.string()
    .min(1, 'Date of Birth is required')
    .refine((val) => {
      const dob = new Date(val);
      const today = new Date();
      const age18 = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
      return dob <= age18;
    }, { message: 'Member must be at least 18 years old' }),
  country: z.number().min(1, 'Country is required'),
  city: z.string().min(1, 'City is required'),
  personality: z.number().min(1, 'Personality is required'),
  relationshipStatus: z.number().min(1, 'Relationship Status is required'),
  ethnicity: z.number().min(1, 'Ethnicity is required'),
  hairColor: z.number().min(1, 'Hair Color is required'),
  appearance: z.number().min(1, 'Appearance is required'),
  eyeColor: z.number().min(1, 'Eye Color is required'),
  bodyType: z.number().min(1, 'Body Type is required'),
  smokingHabit: z.number().min(1, 'Smoking Habit is required'),
  drinkingHabit: z.number().min(1, 'Drinking Habit is required'),
  bestFeature: z.number().min(1, 'Best Feature is required'),
  bodyArt: z.number().min(1, 'Body Art is required'),
  sexualOrientation: z.number().min(1, 'Sexual Orientation is required'),
  height: z.number().min(1, "Height is required"),
  weight: z.number().min(1, "Weight is required"),
  kids: z.number().min(0, 'Kids is required'),
  interest: z.number().min(1, 'Interests is required'),
  aboutMe: z.string().optional(),
})

type MemberProfileFormValues = z.infer<typeof memberProfileSchema>
type MasterOption = { id: string | number; title: string };

export default function MemberProfileForm({ member: data }: MemberProfileFormProps) {
  const [basicInfoOpen, setBasicInfoOpen] = useState(true)
  const [locationOpen, setLocationOpen] = useState(true)
  const [personalAttributesOpen, setPersonalAttributesOpen] = useState(true)
  const [dob, setdob] = useState<any>(undefined
    // member.dob ? new Date(member.dob) : undefined
  )
  const navigate = useNavigate()

  const { memberId } = useParams({ strict: false })

  const { data: member } = getMemberDetails(memberId)
  const { mutateAsync: updateMemberMutation } = updateMemberlApi()
  const { data: masterData } = getMasterApi()
  const { data: countryList } = getCountryList()

  const queryClient = useQueryClient()


  const masterOptions = useMemo(() => {
    const master = masterData?.master || {};
    return {
      appearance: master.appearance || [],
      relationshipStatus: master.relationship_status || [],
      personality: master.personality || [],
      eyeColor: master.eye_color || [],
      bodyType: master.body_type || [],
      hairColor: master.hair_color || [],
      smokingHabits: master.smoking_habits || [],
      drinkingHabits: master.drinking_habits || [],
      bestFeature: master.best_feature || [],
      bodyArt: master.body_art || [],
      sexualOrientation: master.sexual_orientation || [],
      ethnicity: master.ethnicity || [],
      seekingFor: master.seeking_for || [],
      gender: master.gender || [],
      interest: master.interest || [],
    };
  }, [masterData]);


  const form = useForm<MemberProfileFormValues>({
    resolver: zodResolver(memberProfileSchema),
    defaultValues: {
      username: member?.username || '',
      email: member?.email || '',
      gender: member?.customer_profile?.gender?.id || 0,
      seekingFor: member?.customer_profile?.seekingFor?.id || 0,
      dob: member?.customer_profile?.dob || '',
      country: member?.country?.id ? Number(member?.country.id) : undefined,
      city: member?.customer_profile?.city || '',
      personality: member?.customer_profile?.personality?.id || 0,
      relationshipStatus: member?.customer_profile?.relationshipStatus?.id || 0,
      ethnicity: member?.customer_profile?.ethnicity?.id || 0,
      hairColor: member?.customer_profile?.hairColor?.id || 0,
      appearance: member?.customer_profile?.appearance?.id || 0,
      eyeColor: member?.customer_profile?.eyeColor?.id || 0,
      bodyType: member?.customer_profile?.bodyType?.id || 0,
      smokingHabit: member?.customer_profile?.smokingHabit?.id || 0,
      drinkingHabit: member?.customer_profile?.drinkingHabit?.id || 0,
      bestFeature: member?.customer_profile?.bestFeature?.id || 0,
      bodyArt: member?.customer_profile?.bodyArt?.id || 0,
      sexualOrientation: member?.customer_profile?.sexualOrientation?.id || 0,
      height: member?.customer_profile?.height ? Number(member.customer_profile.height) : undefined,
      weight: member?.customer_profile?.weight ? Number(member.customer_profile.weight) : undefined,
      kids: member?.customer_profile?.kids?.id || 0,
      interest: member?.customer_profile?.interest?.id ? Number(member.customer_profile.interest.id) : undefined,
      aboutMe: member?.customer_profile?.aboutMe || '',
    },
  })
  const { control, handleSubmit, reset, setValue } = form;


  useEffect(() => {
    if (member?.customer_profile) {
      const profile = member.customer_profile;
      // List of dropdown keys that use .id (country removed)
      const dropdownKeys = [
        'appearance',
        'hairColor',
        'eyeColor',
        'seekingFor',
        'personality',
        'relationshipStatus',
        'ethnicity',
        'bodyType',
        'smokingHabit',
        'drinkingHabit',
        'bestFeature',
        'bodyArt',
        'sexualOrientation',
        'gender',
        'interest',
      ];
      dropdownKeys.forEach((key: any) => {
        if (profile[key] && profile[key].id !== undefined) {
          setValue(key, profile[key].id);
        }
      });
      // Set country from member.country
      if (member.country && member.country.id !== undefined) {
        setValue('country', member.country.id);
      }
      // Set other fields as needed
      if (profile.kids) setValue('kids', profile.kids);
      if (profile.aboutMe) setValue('aboutMe', profile.aboutMe);
      if (member.height !== undefined) setValue('height', Number(member.height));
      if (member.weight !== undefined) setValue('weight', Number(member.weight));
      if (member.city) setValue('city', member.city);
    }
  }, [member?.username, setValue]);

  const onSubmit = async (values: MemberProfileFormValues) => {
    try {
      // Convert specified fields to numbers before sending to API
      const numberFields = [
        'gender',
        'seekingFor',
        'appearance',
        'hairColor',
        'eyeColor',
        'bodyType',
        'relationshipStatus',
        'bestFeature',
        'sexualOrientation',
        'personality',
        'ethnicity',
        'kids',
        'country',
      ];
      const payload: any = { ...values, id: Number(memberId) };
      numberFields.forEach((key) => {
        if (payload[key] !== undefined && payload[key] !== null && payload[key] !== "") {
          payload[key] = Number(payload[key]);
        }
      });
      const response: any = await updateMemberMutation(payload);
      if (response?.success) {
        queryClient.invalidateQueries({ queryKey: ['member-details'] })
        toast.success('Member profile has been updated!')
      }
    } catch (error) {
      toast.error('Failed to update member profile')
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-6">
          {/* Basic Information Section */}
          <Card>
            <Collapsible open={basicInfoOpen} onOpenChange={setBasicInfoOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Basic Information</CardTitle>
                    {basicInfoOpen ? (
                      <ChevronUpIcon className="h-4 w-4" />
                    ) : (
                      <ChevronDownIcon className="h-4 w-4" />
                    )}
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    <div className="space-y-2">
                      <FormField name="dob" control={control} render={({ field }) => {
                        const [open, setOpen] = useState(false);
                        const selectedDate = field.value ? new Date(field.value) : undefined;
                        const displayValue = selectedDate ? format(selectedDate, "yyyy-MM-dd") : "";
                        return (
                          <FormItem>
                            <FormLabel>Date of Birth</FormLabel>
                            <FormControl className="w-full">
                              <Popover open={open} onOpenChange={setOpen}>
                                <PopoverTrigger asChild>
                                  <div className="relative w-full">
                                    <Input
                                      readOnly
                                      value={displayValue}
                                      placeholder="YYYY-MM-DD"
                                      className="pr-10 cursor-pointer"
                                      onClick={() => setOpen(true)}
                                    />
                                    <CalendarIcon
                                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                                      width={20}
                                      height={20}
                                    />
                                  </div>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                  <Calendar
                                    mode="single"
                                    selected={selectedDate}
                                    className="rounded-md border shadow-sm"
                                    captionLayout="dropdown"
                                    onSelect={(date) => {
                                      if (date) {
                                        field.onChange(date.toISOString());
                                        setOpen(false);
                                      }
                                    }}
                                  />
                                </PopoverContent>
                              </Popover>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        );
                      }} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="username" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Username</FormLabel>
                          <FormControl>
                            <Input disabled id="username" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="email" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email ID</FormLabel>
                          <FormControl>
                            <Input disabled id="email" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="gender" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Select Gender</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value}>
                            <SelectTrigger style={{ width: '100%' }}>
                              <SelectValue placeholder="Select gender">
                                {field.value
                                  ? masterOptions.gender.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select gender"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.gender.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="seekingFor" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Seeking For</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value}>
                            <SelectTrigger style={{ width: '100%' }}>
                              <SelectValue placeholder="Select seeking for">
                                {field.value ? masterOptions.seekingFor.find((opt: any) => String(opt.id) === String(field.value))?.title : "Select seeking for"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.seekingFor.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                  </div>

                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Location Section */}
          <Card>
            <Collapsible open={locationOpen} onOpenChange={setLocationOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Location</CardTitle>
                    {locationOpen ? (
                      <ChevronUpIcon className="h-4 w-4" />
                    ) : (
                      <ChevronDownIcon className="h-4 w-4" />
                    )}
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <FormField name="country" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Select Country</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value !== undefined ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: '100%' }}>
                              <SelectValue placeholder="Select country">
                                {field.value
                                  ? countryList?.country?.find((c: any) => String(c.id) === String(field.value))?.name
                                  : "Select country"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {countryList?.country?.map((c: any) => (
                                <SelectItem key={c.id} value={String(c.id)}>{c.name}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="city" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input id="city" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Personal Attributes Section */}
          <Card>
            <Collapsible open={personalAttributesOpen} onOpenChange={setPersonalAttributesOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Personal Attributes</CardTitle>
                    {personalAttributesOpen ? (
                      <ChevronUpIcon className="h-4 w-4" />
                    ) : (
                      <ChevronDownIcon className="h-4 w-4" />
                    )}
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <FormField name="appearance" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Appearance</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value || ""}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Appearance">
                                {field.value
                                  ? masterOptions.appearance.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Appearance"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.appearance.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="hairColor" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Hair Color</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Hair Color">
                                {field.value
                                  ? masterOptions.hairColor.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Hair Color"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.hairColor.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="eyeColor" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Eye Color</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Eye Color">
                                {field.value
                                  ? masterOptions.eyeColor.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Eye Color"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.eyeColor.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="bodyType" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Body Type</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Body Type">
                                {field.value
                                  ? masterOptions.bodyType.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Body Type"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.bodyType.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="bestFeature" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Best Feature</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Best Feature">
                                {field.value
                                  ? masterOptions.bestFeature.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Best Feature"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.bestFeature.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="bodyArt" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Body Art</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Body Art">
                                {field.value
                                  ? masterOptions.bodyArt.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Body Art"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.bodyArt.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="height" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Height (CM)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              className="no-spinner"
                              min={100}
                              max={250}
                              step={1}
                              placeholder="Enter height in cm"
                              value={field.value ?? ''}
                              onChange={e => field.onChange(e.target.value === '' ? undefined : Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="weight" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Weight (KG)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              className="no-spinner"
                              min={30}
                              max={300}
                              step={1}
                              placeholder="Enter weight in kg"
                              value={field.value ?? ''}
                              onChange={e => field.onChange(e.target.value === '' ? undefined : Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="relationshipStatus" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Relationship Status</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Relationship Status">
                                {field.value
                                  ? masterOptions.relationshipStatus.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Relationship Status"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.relationshipStatus.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="smokingHabit" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Smoking Habit</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Smoking Habit">
                                {field.value
                                  ? masterOptions.smokingHabits.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Smoking Habit"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.smokingHabits.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="drinkingHabit" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Drinking Habit</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Drinking Habit">
                                {field.value
                                  ? masterOptions.drinkingHabits.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Drinking Habit"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.drinkingHabits.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="sexualOrientation" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sexual Orientation</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Sexual Orientation">
                                {field.value
                                  ? masterOptions.sexualOrientation.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Sexual Orientation"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.sexualOrientation.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>

                    <div className="space-y-2">
                      <FormField name="personality" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Personality</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Personality">
                                {field.value
                                  ? masterOptions.personality.find((opt: MasterOption) => String(opt.id) === String(field.value))?.title
                                  : "Select Personality"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.personality.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="ethnicity" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ethnicity</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Ethnicity">
                                {field.value
                                  ? masterOptions.ethnicity.find((opt: any) => String(opt.id) === String(field.value))?.title
                                  : "Select Ethnicity"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.ethnicity.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="interest" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Interests</FormLabel>
                          <Select onValueChange={val => field.onChange(Number(val))} value={field.value !== undefined ? String(field.value) : ''}>
                            <SelectTrigger style={{ width: "100%" }}>
                              <SelectValue placeholder="Select Interest">
                                {field.value
                                  ? masterOptions.interest.find((opt: MasterOption) => String(opt.id) === String(field.value))?.title
                                  : "Select Interest"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {masterOptions.interest.map((opt: MasterOption) => (
                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                    <div className="space-y-2">
                      <FormField name="kids" control={control} render={({ field }) => (
                        <FormItem>
                          <FormLabel>Kids</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={0}
                              step={1}
                              className="no-spinner"
                              placeholder="Enter number of kids"
                              value={field.value ?? ''}
                              onChange={e => {
                                const val = e.target.value;
                                // Only allow integers
                                if (val === '' || /^\d+$/.test(val)) {
                                  field.onChange(val === '' ? undefined : Number(val));
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )} />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <FormField name="aboutMe" control={control} render={({ field }) => (
                      <FormItem>
                        <FormLabel>About Me</FormLabel>
                        <FormControl>
                          <Textarea
                            id="aboutMe"
                            {...field}
                            placeholder="Enter about you..."
                            rows={8}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>
                  {/* Action Buttons */}
                  <div className="flex justify-end gap-3 pt-4">
                    <Button onClick={() => {
                      navigate({ to: END_POINTS.MEMBERS })
                    }} variant={"secondary"} style={{ cursor: 'pointer' }}>
                      Cancel
                    </Button>
                    <Button type="submit">Save</Button>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        </div>
      </form>
    </Form>
  )
}
