import { IconStar, IconTrash } from '@tabler/icons-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { toast } from 'sonner'
import { useQueryClient } from '@tanstack/react-query'
import {
    setDefaultModelImageApi,
    deleteModelImageApi,
    getModelDetails
} from '../../api'
import { S3_BASE_URL } from '@/features/members/utils/utilities'
import { Settings } from 'lucide-react'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { useState } from 'react'

interface ProfileImagesListProps {
    modelId: string
    modelName?: string
}

export default function ProfileImagesList({ modelId, modelName: _mName }: ProfileImagesListProps) {
    const queryClient = useQueryClient()
    const [show, setShow] = useState<boolean>(false)
    const [currentImageId, setCurrentImageId] = useState('')
    // API queries and mutations
    const { data: { username: modelName = " ", images: profileImages = [] } = {}, isLoading } = getModelDetails(modelId)
    const { mutateAsync: setDefaultModelImageMutation } = setDefaultModelImageApi()
    const { mutateAsync: deleteModelImageMutation } = deleteModelImageApi()

    const handleSetAsDefault = async (imagePath: string) => {
        try {
            await setDefaultModelImageMutation({
                modelId: modelId,
                imagePath
            })
            toast.success("Set as default cover successfully!")
            queryClient.invalidateQueries({ queryKey: ['model-details'] })

        } catch (err) {
            toast.error("Failed to set as default cover")
        }
    }

    const handleDeleteImage = async () => {
        try {
            await deleteModelImageMutation({
                modelId: modelId,
                imageId: currentImageId
            })
            toast.success("Image deleted successfully!")
            queryClient.invalidateQueries({ queryKey: ['model-details'] })

        } catch (err) {
            toast.error("Failed to delete image")
        }
        setShow(false)
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">Image Gallery</CardTitle>
                <p className="text-sm text-muted-foreground">
                    Manage {modelName}'s images
                </p>
            </CardHeader>
            <CardContent>
                {isLoading ? (
                    <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                        <p className="text-muted-foreground mt-2">Loading images...</p>
                    </div>
                ) : profileImages.length === 0 ? (
                    <div className="text-center py-8">
                        <p className="text-muted-foreground">No profile images uploaded yet</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mt-4">
                        {profileImages.map((image: any) => (
                            <div key={image} className="relative group">
                                {/* Image Container */}
                                <div className="relative aspect-square rounded-lg overflow-hidden bg-gray-100">
                                    <img
                                        key={image?.id}
                                        src={S3_BASE_URL + image?.image}
                                        alt={`Profile ${image.id}`}
                                        className="w-full h-full object-cover"
                                        loading="lazy"
                                    />

                                    {/* Default Badge */}
                                    {image.isDefault && (
                                        <div className="absolute top-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded flex items-center gap-1">
                                            <IconStar className="h-3 w-3" />
                                            Default
                                        </div>
                                    )}

                                    {/* Menu Button */}
                                    <div className="absolute top-2 right-2">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>

                                                <Settings className="h-4 w-4" />
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end" className="w-48">
                                                {!image.isDefault && (
                                                    <>
                                                        <DropdownMenuItem
                                                            onClick={() => handleSetAsDefault(image.image)}
                                                            className="flex items-center gap-2"
                                                        >
                                                            <IconStar className="h-4 w-4" />
                                                            Set as Default Cover
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                    </>
                                                )}
                                                <DropdownMenuItem
                                                    onClick={() => {
                                                        setCurrentImageId(image?.id)
                                                        setShow(true)
                                                    }
                                                    }
                                                    className="flex items-center gap-2 text-destructive focus:text-destructive"
                                                >
                                                    <IconTrash className="h-4 w-4" />
                                                    Delete Image
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </div>

                                {/* Image Info */}
                                <div className="mt-2 text-xs text-muted-foreground">
                                    {/* <p className="truncate">{image.filename}</p> */}
                                    {/* <p>{new Date(image.uploadedAt).toLocaleDateString()}</p> */}
                                </div>

                                <ConfirmDialog
                                    open={show}
                                    onOpenChange={setShow}
                                    title="Are you sure?"
                                    desc="This action cannot be undone. This will permanently delete the image."
                                    confirmText="Delete"
                                    cancelBtnText="Cancel"
                                    destructive
                                    isLoading={false}
                                    handleConfirm={handleDeleteImage}
                                />
                            </div>
                        ))}
                    </div>
                )
                }
            </CardContent >


        </Card >
    )
}
