/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AuthenticatedRouteRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as AuthenticatedContactUsRouteImport } from './routes/_authenticated/contact-us'
import { Route as AuthenticatedAdminLoginActivityRouteImport } from './routes/_authenticated/admin-login-activity'
import { Route as errors503RouteImport } from './routes/(errors)/503'
import { Route as errors500RouteImport } from './routes/(errors)/500'
import { Route as errors404RouteImport } from './routes/(errors)/404'
import { Route as errors403RouteImport } from './routes/(errors)/403'
import { Route as errors401RouteImport } from './routes/(errors)/401'
import { Route as authSignUpRouteImport } from './routes/(auth)/sign-up'
import { Route as authSignInRouteImport } from './routes/(auth)/sign-in'
import { Route as authResetPasswordRouteImport } from './routes/(auth)/reset-password'
import { Route as authOtpRouteImport } from './routes/(auth)/otp'
import { Route as authForgotPasswordRouteImport } from './routes/(auth)/forgot-password'
import { Route as AuthenticatedSmiliesRouteRouteImport } from './routes/_authenticated/smilies/route'
import { Route as AuthenticatedSettingsRouteRouteImport } from './routes/_authenticated/settings/route'
import { Route as AuthenticatedSessionsRouteRouteImport } from './routes/_authenticated/sessions/route'
import { Route as AuthenticatedPackagesRouteRouteImport } from './routes/_authenticated/packages/route'
import { Route as AuthenticatedModeratorsRouteRouteImport } from './routes/_authenticated/moderators/route'
import { Route as AuthenticatedModelsRouteRouteImport } from './routes/_authenticated/models/route'
import { Route as AuthenticatedMembersRouteRouteImport } from './routes/_authenticated/members/route'
import { Route as AuthenticatedGiftsRouteRouteImport } from './routes/_authenticated/gifts/route'
import { Route as AuthenticatedGifsRouteRouteImport } from './routes/_authenticated/gifs/route'
import { Route as AuthenticatedFlirtMessagesRouteRouteImport } from './routes/_authenticated/flirt-messages/route'
import { Route as AuthenticatedCurrenciesRouteRouteImport } from './routes/_authenticated/currencies/route'
import { Route as AuthenticatedBotMessagesRouteRouteImport } from './routes/_authenticated/bot-messages/route'
import { Route as AuthenticatedAnnouncementsRouteRouteImport } from './routes/_authenticated/announcements/route'
import { Route as AuthenticatedUsersIndexRouteImport } from './routes/_authenticated/users/index'
import { Route as AuthenticatedSmiliesIndexRouteImport } from './routes/_authenticated/smilies/index'
import { Route as AuthenticatedSettingsIndexRouteImport } from './routes/_authenticated/settings/index'
import { Route as AuthenticatedSessionsIndexRouteImport } from './routes/_authenticated/sessions/index'
import { Route as AuthenticatedPackagesIndexRouteImport } from './routes/_authenticated/packages/index'
import { Route as AuthenticatedModeratorsIndexRouteImport } from './routes/_authenticated/moderators/index'
import { Route as AuthenticatedModelsIndexRouteImport } from './routes/_authenticated/models/index'
import { Route as AuthenticatedMembersIndexRouteImport } from './routes/_authenticated/members/index'
import { Route as AuthenticatedHelpCenterIndexRouteImport } from './routes/_authenticated/help-center/index'
import { Route as AuthenticatedGiftsIndexRouteImport } from './routes/_authenticated/gifts/index'
import { Route as AuthenticatedGifsIndexRouteImport } from './routes/_authenticated/gifs/index'
import { Route as AuthenticatedFlirtMessagesIndexRouteImport } from './routes/_authenticated/flirt-messages/index'
import { Route as AuthenticatedCurrenciesIndexRouteImport } from './routes/_authenticated/currencies/index'
import { Route as AuthenticatedChatsIndexRouteImport } from './routes/_authenticated/chats/index'
import { Route as AuthenticatedBotMessagesIndexRouteImport } from './routes/_authenticated/bot-messages/index'
import { Route as AuthenticatedAnnouncementsIndexRouteImport } from './routes/_authenticated/announcements/index'
import { Route as AuthenticatedSmiliesAddRouteImport } from './routes/_authenticated/smilies/add'
import { Route as AuthenticatedSettingsNotificationsRouteImport } from './routes/_authenticated/settings/notifications'
import { Route as AuthenticatedSettingsDisplayRouteImport } from './routes/_authenticated/settings/display'
import { Route as AuthenticatedSettingsAppearanceRouteImport } from './routes/_authenticated/settings/appearance'
import { Route as AuthenticatedSettingsAccountRouteImport } from './routes/_authenticated/settings/account'
import { Route as AuthenticatedSessionsLobyRouteImport } from './routes/_authenticated/sessions/loby'
import { Route as AuthenticatedSessionsChatModRouteImport } from './routes/_authenticated/sessions/chat-mod'
import { Route as AuthenticatedPackagesAddRouteImport } from './routes/_authenticated/packages/add'
import { Route as AuthenticatedModeratorsReplyMessagesRouteImport } from './routes/_authenticated/moderators/reply-messages'
import { Route as AuthenticatedModeratorsLoginActivityRouteImport } from './routes/_authenticated/moderators/login-activity'
import { Route as AuthenticatedModeratorsDomainRouteImport } from './routes/_authenticated/moderators/domain'
import { Route as AuthenticatedModeratorsCreateModeratorRouteImport } from './routes/_authenticated/moderators/create-moderator'
import { Route as AuthenticatedModeratorsChatGroupRouteImport } from './routes/_authenticated/moderators/chat-group'
import { Route as AuthenticatedModelsCreateModelRouteImport } from './routes/_authenticated/models/create-model'
import { Route as AuthenticatedGiftsAddRouteImport } from './routes/_authenticated/gifts/add'
import { Route as AuthenticatedGifsAddRouteImport } from './routes/_authenticated/gifs/add'
import { Route as AuthenticatedFlirtMessagesAddRouteImport } from './routes/_authenticated/flirt-messages/add'
import { Route as AuthenticatedBotMessagesAddRouteImport } from './routes/_authenticated/bot-messages/add'
import { Route as AuthenticatedAnnouncementsWhiteLabelsRouteImport } from './routes/_authenticated/announcements/white-labels'
import { Route as AuthenticatedAnnouncementsModeratorsRouteImport } from './routes/_authenticated/announcements/moderators'
import { Route as AuthenticatedAnnouncementsAffiliatesRouteImport } from './routes/_authenticated/announcements/affiliates'
import { Route as AuthenticatedSmiliesUpdateMsgIdRouteImport } from './routes/_authenticated/smilies/update.$msgId'
import { Route as AuthenticatedPackagesUpdateMsgIdRouteImport } from './routes/_authenticated/packages/update.$msgId'
import { Route as AuthenticatedModeratorsUpdateModeratorModeratorIdRouteImport } from './routes/_authenticated/moderators/update-moderator.$moderatorId'
import { Route as AuthenticatedModeratorsProfileModeratorIdRouteImport } from './routes/_authenticated/moderators/profile.$moderatorId'
import { Route as AuthenticatedModelsUpdateModelModelIdRouteImport } from './routes/_authenticated/models/update-model.$modelId'
import { Route as AuthenticatedModelsProfileModelIdRouteImport } from './routes/_authenticated/models/profile.$modelId'
import { Route as AuthenticatedModelsProfileImagesModelIdRouteImport } from './routes/_authenticated/models/profile-images.$modelId'
import { Route as AuthenticatedMembersProfileMemberIdRouteImport } from './routes/_authenticated/members/profile.$memberId'
import { Route as AuthenticatedGiftsUpdateMsgIdRouteImport } from './routes/_authenticated/gifts/update.$msgId'
import { Route as AuthenticatedGifsUpdateMsgIdRouteImport } from './routes/_authenticated/gifs/update.$msgId'
import { Route as AuthenticatedFlirtMessagesUpdateMsgIdRouteImport } from './routes/_authenticated/flirt-messages/update.$msgId'
import { Route as AuthenticatedBotMessagesUpdateMsgIdRouteImport } from './routes/_authenticated/bot-messages/update.$msgId'
import { Route as AuthenticatedMembersProfileMemberIdPicturesRouteImport } from './routes/_authenticated/members/profile.$memberId.pictures'
import { Route as AuthenticatedMembersProfileMemberIdMessagesRouteImport } from './routes/_authenticated/members/profile.$memberId.messages'
import { Route as AuthenticatedMembersProfileMemberIdCreditsRouteImport } from './routes/_authenticated/members/profile.$memberId.credits'
import { Route as AuthenticatedMembersProfileMemberIdMessagesConversationIdRouteImport } from './routes/_authenticated/members/profile.$memberId.messages.$conversationId'

const AuthenticatedRouteRoute = AuthenticatedRouteRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedContactUsRoute = AuthenticatedContactUsRouteImport.update({
  id: '/contact-us',
  path: '/contact-us',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedAdminLoginActivityRoute =
  AuthenticatedAdminLoginActivityRouteImport.update({
    id: '/admin-login-activity',
    path: '/admin-login-activity',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const errors503Route = errors503RouteImport.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRouteImport,
} as any)
const errors500Route = errors500RouteImport.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRouteImport,
} as any)
const errors404Route = errors404RouteImport.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRouteImport,
} as any)
const errors403Route = errors403RouteImport.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRouteImport,
} as any)
const errors401Route = errors401RouteImport.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRouteImport,
} as any)
const authSignUpRoute = authSignUpRouteImport.update({
  id: '/(auth)/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRouteImport,
} as any)
const authSignInRoute = authSignInRouteImport.update({
  id: '/(auth)/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRouteImport,
} as any)
const authResetPasswordRoute = authResetPasswordRouteImport.update({
  id: '/(auth)/reset-password',
  path: '/reset-password',
  getParentRoute: () => rootRouteImport,
} as any)
const authOtpRoute = authOtpRouteImport.update({
  id: '/(auth)/otp',
  path: '/otp',
  getParentRoute: () => rootRouteImport,
} as any)
const authForgotPasswordRoute = authForgotPasswordRouteImport.update({
  id: '/(auth)/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedSmiliesRouteRoute =
  AuthenticatedSmiliesRouteRouteImport.update({
    id: '/smilies',
    path: '/smilies',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedSettingsRouteRoute =
  AuthenticatedSettingsRouteRouteImport.update({
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedSessionsRouteRoute =
  AuthenticatedSessionsRouteRouteImport.update({
    id: '/sessions',
    path: '/sessions',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedPackagesRouteRoute =
  AuthenticatedPackagesRouteRouteImport.update({
    id: '/packages',
    path: '/packages',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedModeratorsRouteRoute =
  AuthenticatedModeratorsRouteRouteImport.update({
    id: '/moderators',
    path: '/moderators',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedModelsRouteRoute =
  AuthenticatedModelsRouteRouteImport.update({
    id: '/models',
    path: '/models',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedMembersRouteRoute =
  AuthenticatedMembersRouteRouteImport.update({
    id: '/members',
    path: '/members',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedGiftsRouteRoute = AuthenticatedGiftsRouteRouteImport.update({
  id: '/gifts',
  path: '/gifts',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedGifsRouteRoute = AuthenticatedGifsRouteRouteImport.update({
  id: '/gifs',
  path: '/gifs',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedFlirtMessagesRouteRoute =
  AuthenticatedFlirtMessagesRouteRouteImport.update({
    id: '/flirt-messages',
    path: '/flirt-messages',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedCurrenciesRouteRoute =
  AuthenticatedCurrenciesRouteRouteImport.update({
    id: '/currencies',
    path: '/currencies',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedBotMessagesRouteRoute =
  AuthenticatedBotMessagesRouteRouteImport.update({
    id: '/bot-messages',
    path: '/bot-messages',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedAnnouncementsRouteRoute =
  AuthenticatedAnnouncementsRouteRouteImport.update({
    id: '/announcements',
    path: '/announcements',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedUsersIndexRoute = AuthenticatedUsersIndexRouteImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedSmiliesIndexRoute =
  AuthenticatedSmiliesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSmiliesRouteRoute,
  } as any)
const AuthenticatedSettingsIndexRoute =
  AuthenticatedSettingsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSessionsIndexRoute =
  AuthenticatedSessionsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSessionsRouteRoute,
  } as any)
const AuthenticatedPackagesIndexRoute =
  AuthenticatedPackagesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedPackagesRouteRoute,
  } as any)
const AuthenticatedModeratorsIndexRoute =
  AuthenticatedModeratorsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModelsIndexRoute =
  AuthenticatedModelsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedMembersIndexRoute =
  AuthenticatedMembersIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMembersRouteRoute,
  } as any)
const AuthenticatedHelpCenterIndexRoute =
  AuthenticatedHelpCenterIndexRouteImport.update({
    id: '/help-center/',
    path: '/help-center/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedGiftsIndexRoute = AuthenticatedGiftsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedGiftsRouteRoute,
} as any)
const AuthenticatedGifsIndexRoute = AuthenticatedGifsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedGifsRouteRoute,
} as any)
const AuthenticatedFlirtMessagesIndexRoute =
  AuthenticatedFlirtMessagesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedFlirtMessagesRouteRoute,
  } as any)
const AuthenticatedCurrenciesIndexRoute =
  AuthenticatedCurrenciesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedCurrenciesRouteRoute,
  } as any)
const AuthenticatedChatsIndexRoute = AuthenticatedChatsIndexRouteImport.update({
  id: '/chats/',
  path: '/chats/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedBotMessagesIndexRoute =
  AuthenticatedBotMessagesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedBotMessagesRouteRoute,
  } as any)
const AuthenticatedAnnouncementsIndexRoute =
  AuthenticatedAnnouncementsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedAnnouncementsRouteRoute,
  } as any)
const AuthenticatedSmiliesAddRoute = AuthenticatedSmiliesAddRouteImport.update({
  id: '/add',
  path: '/add',
  getParentRoute: () => AuthenticatedSmiliesRouteRoute,
} as any)
const AuthenticatedSettingsNotificationsRoute =
  AuthenticatedSettingsNotificationsRouteImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsDisplayRoute =
  AuthenticatedSettingsDisplayRouteImport.update({
    id: '/display',
    path: '/display',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsAppearanceRoute =
  AuthenticatedSettingsAppearanceRouteImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsAccountRoute =
  AuthenticatedSettingsAccountRouteImport.update({
    id: '/account',
    path: '/account',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSessionsLobyRoute =
  AuthenticatedSessionsLobyRouteImport.update({
    id: '/loby',
    path: '/loby',
    getParentRoute: () => AuthenticatedSessionsRouteRoute,
  } as any)
const AuthenticatedSessionsChatModRoute =
  AuthenticatedSessionsChatModRouteImport.update({
    id: '/chat-mod',
    path: '/chat-mod',
    getParentRoute: () => AuthenticatedSessionsRouteRoute,
  } as any)
const AuthenticatedPackagesAddRoute =
  AuthenticatedPackagesAddRouteImport.update({
    id: '/add',
    path: '/add',
    getParentRoute: () => AuthenticatedPackagesRouteRoute,
  } as any)
const AuthenticatedModeratorsReplyMessagesRoute =
  AuthenticatedModeratorsReplyMessagesRouteImport.update({
    id: '/reply-messages',
    path: '/reply-messages',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsLoginActivityRoute =
  AuthenticatedModeratorsLoginActivityRouteImport.update({
    id: '/login-activity',
    path: '/login-activity',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsDomainRoute =
  AuthenticatedModeratorsDomainRouteImport.update({
    id: '/domain',
    path: '/domain',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsCreateModeratorRoute =
  AuthenticatedModeratorsCreateModeratorRouteImport.update({
    id: '/create-moderator',
    path: '/create-moderator',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsChatGroupRoute =
  AuthenticatedModeratorsChatGroupRouteImport.update({
    id: '/chat-group',
    path: '/chat-group',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModelsCreateModelRoute =
  AuthenticatedModelsCreateModelRouteImport.update({
    id: '/create-model',
    path: '/create-model',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedGiftsAddRoute = AuthenticatedGiftsAddRouteImport.update({
  id: '/add',
  path: '/add',
  getParentRoute: () => AuthenticatedGiftsRouteRoute,
} as any)
const AuthenticatedGifsAddRoute = AuthenticatedGifsAddRouteImport.update({
  id: '/add',
  path: '/add',
  getParentRoute: () => AuthenticatedGifsRouteRoute,
} as any)
const AuthenticatedFlirtMessagesAddRoute =
  AuthenticatedFlirtMessagesAddRouteImport.update({
    id: '/add',
    path: '/add',
    getParentRoute: () => AuthenticatedFlirtMessagesRouteRoute,
  } as any)
const AuthenticatedBotMessagesAddRoute =
  AuthenticatedBotMessagesAddRouteImport.update({
    id: '/add',
    path: '/add',
    getParentRoute: () => AuthenticatedBotMessagesRouteRoute,
  } as any)
const AuthenticatedAnnouncementsWhiteLabelsRoute =
  AuthenticatedAnnouncementsWhiteLabelsRouteImport.update({
    id: '/white-labels',
    path: '/white-labels',
    getParentRoute: () => AuthenticatedAnnouncementsRouteRoute,
  } as any)
const AuthenticatedAnnouncementsModeratorsRoute =
  AuthenticatedAnnouncementsModeratorsRouteImport.update({
    id: '/moderators',
    path: '/moderators',
    getParentRoute: () => AuthenticatedAnnouncementsRouteRoute,
  } as any)
const AuthenticatedAnnouncementsAffiliatesRoute =
  AuthenticatedAnnouncementsAffiliatesRouteImport.update({
    id: '/affiliates',
    path: '/affiliates',
    getParentRoute: () => AuthenticatedAnnouncementsRouteRoute,
  } as any)
const AuthenticatedSmiliesUpdateMsgIdRoute =
  AuthenticatedSmiliesUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedSmiliesRouteRoute,
  } as any)
const AuthenticatedPackagesUpdateMsgIdRoute =
  AuthenticatedPackagesUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedPackagesRouteRoute,
  } as any)
const AuthenticatedModeratorsUpdateModeratorModeratorIdRoute =
  AuthenticatedModeratorsUpdateModeratorModeratorIdRouteImport.update({
    id: '/update-moderator/$moderatorId',
    path: '/update-moderator/$moderatorId',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsProfileModeratorIdRoute =
  AuthenticatedModeratorsProfileModeratorIdRouteImport.update({
    id: '/profile/$moderatorId',
    path: '/profile/$moderatorId',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModelsUpdateModelModelIdRoute =
  AuthenticatedModelsUpdateModelModelIdRouteImport.update({
    id: '/update-model/$modelId',
    path: '/update-model/$modelId',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedModelsProfileModelIdRoute =
  AuthenticatedModelsProfileModelIdRouteImport.update({
    id: '/profile/$modelId',
    path: '/profile/$modelId',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedModelsProfileImagesModelIdRoute =
  AuthenticatedModelsProfileImagesModelIdRouteImport.update({
    id: '/profile-images/$modelId',
    path: '/profile-images/$modelId',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdRoute =
  AuthenticatedMembersProfileMemberIdRouteImport.update({
    id: '/profile/$memberId',
    path: '/profile/$memberId',
    getParentRoute: () => AuthenticatedMembersRouteRoute,
  } as any)
const AuthenticatedGiftsUpdateMsgIdRoute =
  AuthenticatedGiftsUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedGiftsRouteRoute,
  } as any)
const AuthenticatedGifsUpdateMsgIdRoute =
  AuthenticatedGifsUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedGifsRouteRoute,
  } as any)
const AuthenticatedFlirtMessagesUpdateMsgIdRoute =
  AuthenticatedFlirtMessagesUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedFlirtMessagesRouteRoute,
  } as any)
const AuthenticatedBotMessagesUpdateMsgIdRoute =
  AuthenticatedBotMessagesUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedBotMessagesRouteRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdPicturesRoute =
  AuthenticatedMembersProfileMemberIdPicturesRouteImport.update({
    id: '/pictures',
    path: '/pictures',
    getParentRoute: () => AuthenticatedMembersProfileMemberIdRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdMessagesRoute =
  AuthenticatedMembersProfileMemberIdMessagesRouteImport.update({
    id: '/messages',
    path: '/messages',
    getParentRoute: () => AuthenticatedMembersProfileMemberIdRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdCreditsRoute =
  AuthenticatedMembersProfileMemberIdCreditsRouteImport.update({
    id: '/credits',
    path: '/credits',
    getParentRoute: () => AuthenticatedMembersProfileMemberIdRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute =
  AuthenticatedMembersProfileMemberIdMessagesConversationIdRouteImport.update({
    id: '/$conversationId',
    path: '/$conversationId',
    getParentRoute: () => AuthenticatedMembersProfileMemberIdMessagesRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/announcements': typeof AuthenticatedAnnouncementsRouteRouteWithChildren
  '/bot-messages': typeof AuthenticatedBotMessagesRouteRouteWithChildren
  '/currencies': typeof AuthenticatedCurrenciesRouteRouteWithChildren
  '/flirt-messages': typeof AuthenticatedFlirtMessagesRouteRouteWithChildren
  '/gifs': typeof AuthenticatedGifsRouteRouteWithChildren
  '/gifts': typeof AuthenticatedGiftsRouteRouteWithChildren
  '/members': typeof AuthenticatedMembersRouteRouteWithChildren
  '/models': typeof AuthenticatedModelsRouteRouteWithChildren
  '/moderators': typeof AuthenticatedModeratorsRouteRouteWithChildren
  '/packages': typeof AuthenticatedPackagesRouteRouteWithChildren
  '/sessions': typeof AuthenticatedSessionsRouteRouteWithChildren
  '/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/smilies': typeof AuthenticatedSmiliesRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/reset-password': typeof authResetPasswordRoute
  '/sign-in': typeof authSignInRoute
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/admin-login-activity': typeof AuthenticatedAdminLoginActivityRoute
  '/contact-us': typeof AuthenticatedContactUsRoute
  '/': typeof AuthenticatedIndexRoute
  '/announcements/affiliates': typeof AuthenticatedAnnouncementsAffiliatesRoute
  '/announcements/moderators': typeof AuthenticatedAnnouncementsModeratorsRoute
  '/announcements/white-labels': typeof AuthenticatedAnnouncementsWhiteLabelsRoute
  '/bot-messages/add': typeof AuthenticatedBotMessagesAddRoute
  '/flirt-messages/add': typeof AuthenticatedFlirtMessagesAddRoute
  '/gifs/add': typeof AuthenticatedGifsAddRoute
  '/gifts/add': typeof AuthenticatedGiftsAddRoute
  '/models/create-model': typeof AuthenticatedModelsCreateModelRoute
  '/moderators/chat-group': typeof AuthenticatedModeratorsChatGroupRoute
  '/moderators/create-moderator': typeof AuthenticatedModeratorsCreateModeratorRoute
  '/moderators/domain': typeof AuthenticatedModeratorsDomainRoute
  '/moderators/login-activity': typeof AuthenticatedModeratorsLoginActivityRoute
  '/moderators/reply-messages': typeof AuthenticatedModeratorsReplyMessagesRoute
  '/packages/add': typeof AuthenticatedPackagesAddRoute
  '/sessions/chat-mod': typeof AuthenticatedSessionsChatModRoute
  '/sessions/loby': typeof AuthenticatedSessionsLobyRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/smilies/add': typeof AuthenticatedSmiliesAddRoute
  '/announcements/': typeof AuthenticatedAnnouncementsIndexRoute
  '/bot-messages/': typeof AuthenticatedBotMessagesIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/currencies/': typeof AuthenticatedCurrenciesIndexRoute
  '/flirt-messages/': typeof AuthenticatedFlirtMessagesIndexRoute
  '/gifs/': typeof AuthenticatedGifsIndexRoute
  '/gifts/': typeof AuthenticatedGiftsIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/members/': typeof AuthenticatedMembersIndexRoute
  '/models/': typeof AuthenticatedModelsIndexRoute
  '/moderators/': typeof AuthenticatedModeratorsIndexRoute
  '/packages/': typeof AuthenticatedPackagesIndexRoute
  '/sessions/': typeof AuthenticatedSessionsIndexRoute
  '/settings/': typeof AuthenticatedSettingsIndexRoute
  '/smilies/': typeof AuthenticatedSmiliesIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/bot-messages/update/$msgId': typeof AuthenticatedBotMessagesUpdateMsgIdRoute
  '/flirt-messages/update/$msgId': typeof AuthenticatedFlirtMessagesUpdateMsgIdRoute
  '/gifs/update/$msgId': typeof AuthenticatedGifsUpdateMsgIdRoute
  '/gifts/update/$msgId': typeof AuthenticatedGiftsUpdateMsgIdRoute
  '/members/profile/$memberId': typeof AuthenticatedMembersProfileMemberIdRouteWithChildren
  '/models/profile-images/$modelId': typeof AuthenticatedModelsProfileImagesModelIdRoute
  '/models/profile/$modelId': typeof AuthenticatedModelsProfileModelIdRoute
  '/models/update-model/$modelId': typeof AuthenticatedModelsUpdateModelModelIdRoute
  '/moderators/profile/$moderatorId': typeof AuthenticatedModeratorsProfileModeratorIdRoute
  '/moderators/update-moderator/$moderatorId': typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRoute
  '/packages/update/$msgId': typeof AuthenticatedPackagesUpdateMsgIdRoute
  '/smilies/update/$msgId': typeof AuthenticatedSmiliesUpdateMsgIdRoute
  '/members/profile/$memberId/credits': typeof AuthenticatedMembersProfileMemberIdCreditsRoute
  '/members/profile/$memberId/messages': typeof AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren
  '/members/profile/$memberId/pictures': typeof AuthenticatedMembersProfileMemberIdPicturesRoute
  '/members/profile/$memberId/messages/$conversationId': typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute
}
export interface FileRoutesByTo {
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/reset-password': typeof authResetPasswordRoute
  '/sign-in': typeof authSignInRoute
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/admin-login-activity': typeof AuthenticatedAdminLoginActivityRoute
  '/contact-us': typeof AuthenticatedContactUsRoute
  '/': typeof AuthenticatedIndexRoute
  '/announcements/affiliates': typeof AuthenticatedAnnouncementsAffiliatesRoute
  '/announcements/moderators': typeof AuthenticatedAnnouncementsModeratorsRoute
  '/announcements/white-labels': typeof AuthenticatedAnnouncementsWhiteLabelsRoute
  '/bot-messages/add': typeof AuthenticatedBotMessagesAddRoute
  '/flirt-messages/add': typeof AuthenticatedFlirtMessagesAddRoute
  '/gifs/add': typeof AuthenticatedGifsAddRoute
  '/gifts/add': typeof AuthenticatedGiftsAddRoute
  '/models/create-model': typeof AuthenticatedModelsCreateModelRoute
  '/moderators/chat-group': typeof AuthenticatedModeratorsChatGroupRoute
  '/moderators/create-moderator': typeof AuthenticatedModeratorsCreateModeratorRoute
  '/moderators/domain': typeof AuthenticatedModeratorsDomainRoute
  '/moderators/login-activity': typeof AuthenticatedModeratorsLoginActivityRoute
  '/moderators/reply-messages': typeof AuthenticatedModeratorsReplyMessagesRoute
  '/packages/add': typeof AuthenticatedPackagesAddRoute
  '/sessions/chat-mod': typeof AuthenticatedSessionsChatModRoute
  '/sessions/loby': typeof AuthenticatedSessionsLobyRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/smilies/add': typeof AuthenticatedSmiliesAddRoute
  '/announcements': typeof AuthenticatedAnnouncementsIndexRoute
  '/bot-messages': typeof AuthenticatedBotMessagesIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/currencies': typeof AuthenticatedCurrenciesIndexRoute
  '/flirt-messages': typeof AuthenticatedFlirtMessagesIndexRoute
  '/gifs': typeof AuthenticatedGifsIndexRoute
  '/gifts': typeof AuthenticatedGiftsIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/members': typeof AuthenticatedMembersIndexRoute
  '/models': typeof AuthenticatedModelsIndexRoute
  '/moderators': typeof AuthenticatedModeratorsIndexRoute
  '/packages': typeof AuthenticatedPackagesIndexRoute
  '/sessions': typeof AuthenticatedSessionsIndexRoute
  '/settings': typeof AuthenticatedSettingsIndexRoute
  '/smilies': typeof AuthenticatedSmiliesIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/bot-messages/update/$msgId': typeof AuthenticatedBotMessagesUpdateMsgIdRoute
  '/flirt-messages/update/$msgId': typeof AuthenticatedFlirtMessagesUpdateMsgIdRoute
  '/gifs/update/$msgId': typeof AuthenticatedGifsUpdateMsgIdRoute
  '/gifts/update/$msgId': typeof AuthenticatedGiftsUpdateMsgIdRoute
  '/members/profile/$memberId': typeof AuthenticatedMembersProfileMemberIdRouteWithChildren
  '/models/profile-images/$modelId': typeof AuthenticatedModelsProfileImagesModelIdRoute
  '/models/profile/$modelId': typeof AuthenticatedModelsProfileModelIdRoute
  '/models/update-model/$modelId': typeof AuthenticatedModelsUpdateModelModelIdRoute
  '/moderators/profile/$moderatorId': typeof AuthenticatedModeratorsProfileModeratorIdRoute
  '/moderators/update-moderator/$moderatorId': typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRoute
  '/packages/update/$msgId': typeof AuthenticatedPackagesUpdateMsgIdRoute
  '/smilies/update/$msgId': typeof AuthenticatedSmiliesUpdateMsgIdRoute
  '/members/profile/$memberId/credits': typeof AuthenticatedMembersProfileMemberIdCreditsRoute
  '/members/profile/$memberId/messages': typeof AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren
  '/members/profile/$memberId/pictures': typeof AuthenticatedMembersProfileMemberIdPicturesRoute
  '/members/profile/$memberId/messages/$conversationId': typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/_authenticated/announcements': typeof AuthenticatedAnnouncementsRouteRouteWithChildren
  '/_authenticated/bot-messages': typeof AuthenticatedBotMessagesRouteRouteWithChildren
  '/_authenticated/currencies': typeof AuthenticatedCurrenciesRouteRouteWithChildren
  '/_authenticated/flirt-messages': typeof AuthenticatedFlirtMessagesRouteRouteWithChildren
  '/_authenticated/gifs': typeof AuthenticatedGifsRouteRouteWithChildren
  '/_authenticated/gifts': typeof AuthenticatedGiftsRouteRouteWithChildren
  '/_authenticated/members': typeof AuthenticatedMembersRouteRouteWithChildren
  '/_authenticated/models': typeof AuthenticatedModelsRouteRouteWithChildren
  '/_authenticated/moderators': typeof AuthenticatedModeratorsRouteRouteWithChildren
  '/_authenticated/packages': typeof AuthenticatedPackagesRouteRouteWithChildren
  '/_authenticated/sessions': typeof AuthenticatedSessionsRouteRouteWithChildren
  '/_authenticated/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/_authenticated/smilies': typeof AuthenticatedSmiliesRouteRouteWithChildren
  '/(auth)/forgot-password': typeof authForgotPasswordRoute
  '/(auth)/otp': typeof authOtpRoute
  '/(auth)/reset-password': typeof authResetPasswordRoute
  '/(auth)/sign-in': typeof authSignInRoute
  '/(auth)/sign-up': typeof authSignUpRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/admin-login-activity': typeof AuthenticatedAdminLoginActivityRoute
  '/_authenticated/contact-us': typeof AuthenticatedContactUsRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/announcements/affiliates': typeof AuthenticatedAnnouncementsAffiliatesRoute
  '/_authenticated/announcements/moderators': typeof AuthenticatedAnnouncementsModeratorsRoute
  '/_authenticated/announcements/white-labels': typeof AuthenticatedAnnouncementsWhiteLabelsRoute
  '/_authenticated/bot-messages/add': typeof AuthenticatedBotMessagesAddRoute
  '/_authenticated/flirt-messages/add': typeof AuthenticatedFlirtMessagesAddRoute
  '/_authenticated/gifs/add': typeof AuthenticatedGifsAddRoute
  '/_authenticated/gifts/add': typeof AuthenticatedGiftsAddRoute
  '/_authenticated/models/create-model': typeof AuthenticatedModelsCreateModelRoute
  '/_authenticated/moderators/chat-group': typeof AuthenticatedModeratorsChatGroupRoute
  '/_authenticated/moderators/create-moderator': typeof AuthenticatedModeratorsCreateModeratorRoute
  '/_authenticated/moderators/domain': typeof AuthenticatedModeratorsDomainRoute
  '/_authenticated/moderators/login-activity': typeof AuthenticatedModeratorsLoginActivityRoute
  '/_authenticated/moderators/reply-messages': typeof AuthenticatedModeratorsReplyMessagesRoute
  '/_authenticated/packages/add': typeof AuthenticatedPackagesAddRoute
  '/_authenticated/sessions/chat-mod': typeof AuthenticatedSessionsChatModRoute
  '/_authenticated/sessions/loby': typeof AuthenticatedSessionsLobyRoute
  '/_authenticated/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/_authenticated/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/_authenticated/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/_authenticated/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/_authenticated/smilies/add': typeof AuthenticatedSmiliesAddRoute
  '/_authenticated/announcements/': typeof AuthenticatedAnnouncementsIndexRoute
  '/_authenticated/bot-messages/': typeof AuthenticatedBotMessagesIndexRoute
  '/_authenticated/chats/': typeof AuthenticatedChatsIndexRoute
  '/_authenticated/currencies/': typeof AuthenticatedCurrenciesIndexRoute
  '/_authenticated/flirt-messages/': typeof AuthenticatedFlirtMessagesIndexRoute
  '/_authenticated/gifs/': typeof AuthenticatedGifsIndexRoute
  '/_authenticated/gifts/': typeof AuthenticatedGiftsIndexRoute
  '/_authenticated/help-center/': typeof AuthenticatedHelpCenterIndexRoute
  '/_authenticated/members/': typeof AuthenticatedMembersIndexRoute
  '/_authenticated/models/': typeof AuthenticatedModelsIndexRoute
  '/_authenticated/moderators/': typeof AuthenticatedModeratorsIndexRoute
  '/_authenticated/packages/': typeof AuthenticatedPackagesIndexRoute
  '/_authenticated/sessions/': typeof AuthenticatedSessionsIndexRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexRoute
  '/_authenticated/smilies/': typeof AuthenticatedSmiliesIndexRoute
  '/_authenticated/users/': typeof AuthenticatedUsersIndexRoute
  '/_authenticated/bot-messages/update/$msgId': typeof AuthenticatedBotMessagesUpdateMsgIdRoute
  '/_authenticated/flirt-messages/update/$msgId': typeof AuthenticatedFlirtMessagesUpdateMsgIdRoute
  '/_authenticated/gifs/update/$msgId': typeof AuthenticatedGifsUpdateMsgIdRoute
  '/_authenticated/gifts/update/$msgId': typeof AuthenticatedGiftsUpdateMsgIdRoute
  '/_authenticated/members/profile/$memberId': typeof AuthenticatedMembersProfileMemberIdRouteWithChildren
  '/_authenticated/models/profile-images/$modelId': typeof AuthenticatedModelsProfileImagesModelIdRoute
  '/_authenticated/models/profile/$modelId': typeof AuthenticatedModelsProfileModelIdRoute
  '/_authenticated/models/update-model/$modelId': typeof AuthenticatedModelsUpdateModelModelIdRoute
  '/_authenticated/moderators/profile/$moderatorId': typeof AuthenticatedModeratorsProfileModeratorIdRoute
  '/_authenticated/moderators/update-moderator/$moderatorId': typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRoute
  '/_authenticated/packages/update/$msgId': typeof AuthenticatedPackagesUpdateMsgIdRoute
  '/_authenticated/smilies/update/$msgId': typeof AuthenticatedSmiliesUpdateMsgIdRoute
  '/_authenticated/members/profile/$memberId/credits': typeof AuthenticatedMembersProfileMemberIdCreditsRoute
  '/_authenticated/members/profile/$memberId/messages': typeof AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren
  '/_authenticated/members/profile/$memberId/pictures': typeof AuthenticatedMembersProfileMemberIdPicturesRoute
  '/_authenticated/members/profile/$memberId/messages/$conversationId': typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/announcements'
    | '/bot-messages'
    | '/currencies'
    | '/flirt-messages'
    | '/gifs'
    | '/gifts'
    | '/members'
    | '/models'
    | '/moderators'
    | '/packages'
    | '/sessions'
    | '/settings'
    | '/smilies'
    | '/forgot-password'
    | '/otp'
    | '/reset-password'
    | '/sign-in'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/admin-login-activity'
    | '/contact-us'
    | '/'
    | '/announcements/affiliates'
    | '/announcements/moderators'
    | '/announcements/white-labels'
    | '/bot-messages/add'
    | '/flirt-messages/add'
    | '/gifs/add'
    | '/gifts/add'
    | '/models/create-model'
    | '/moderators/chat-group'
    | '/moderators/create-moderator'
    | '/moderators/domain'
    | '/moderators/login-activity'
    | '/moderators/reply-messages'
    | '/packages/add'
    | '/sessions/chat-mod'
    | '/sessions/loby'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/smilies/add'
    | '/announcements/'
    | '/bot-messages/'
    | '/chats'
    | '/currencies/'
    | '/flirt-messages/'
    | '/gifs/'
    | '/gifts/'
    | '/help-center'
    | '/members/'
    | '/models/'
    | '/moderators/'
    | '/packages/'
    | '/sessions/'
    | '/settings/'
    | '/smilies/'
    | '/users'
    | '/bot-messages/update/$msgId'
    | '/flirt-messages/update/$msgId'
    | '/gifs/update/$msgId'
    | '/gifts/update/$msgId'
    | '/members/profile/$memberId'
    | '/models/profile-images/$modelId'
    | '/models/profile/$modelId'
    | '/models/update-model/$modelId'
    | '/moderators/profile/$moderatorId'
    | '/moderators/update-moderator/$moderatorId'
    | '/packages/update/$msgId'
    | '/smilies/update/$msgId'
    | '/members/profile/$memberId/credits'
    | '/members/profile/$memberId/messages'
    | '/members/profile/$memberId/pictures'
    | '/members/profile/$memberId/messages/$conversationId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/forgot-password'
    | '/otp'
    | '/reset-password'
    | '/sign-in'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/admin-login-activity'
    | '/contact-us'
    | '/'
    | '/announcements/affiliates'
    | '/announcements/moderators'
    | '/announcements/white-labels'
    | '/bot-messages/add'
    | '/flirt-messages/add'
    | '/gifs/add'
    | '/gifts/add'
    | '/models/create-model'
    | '/moderators/chat-group'
    | '/moderators/create-moderator'
    | '/moderators/domain'
    | '/moderators/login-activity'
    | '/moderators/reply-messages'
    | '/packages/add'
    | '/sessions/chat-mod'
    | '/sessions/loby'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/smilies/add'
    | '/announcements'
    | '/bot-messages'
    | '/chats'
    | '/currencies'
    | '/flirt-messages'
    | '/gifs'
    | '/gifts'
    | '/help-center'
    | '/members'
    | '/models'
    | '/moderators'
    | '/packages'
    | '/sessions'
    | '/settings'
    | '/smilies'
    | '/users'
    | '/bot-messages/update/$msgId'
    | '/flirt-messages/update/$msgId'
    | '/gifs/update/$msgId'
    | '/gifts/update/$msgId'
    | '/members/profile/$memberId'
    | '/models/profile-images/$modelId'
    | '/models/profile/$modelId'
    | '/models/update-model/$modelId'
    | '/moderators/profile/$moderatorId'
    | '/moderators/update-moderator/$moderatorId'
    | '/packages/update/$msgId'
    | '/smilies/update/$msgId'
    | '/members/profile/$memberId/credits'
    | '/members/profile/$memberId/messages'
    | '/members/profile/$memberId/pictures'
    | '/members/profile/$memberId/messages/$conversationId'
  id:
    | '__root__'
    | '/_authenticated'
    | '/_authenticated/announcements'
    | '/_authenticated/bot-messages'
    | '/_authenticated/currencies'
    | '/_authenticated/flirt-messages'
    | '/_authenticated/gifs'
    | '/_authenticated/gifts'
    | '/_authenticated/members'
    | '/_authenticated/models'
    | '/_authenticated/moderators'
    | '/_authenticated/packages'
    | '/_authenticated/sessions'
    | '/_authenticated/settings'
    | '/_authenticated/smilies'
    | '/(auth)/forgot-password'
    | '/(auth)/otp'
    | '/(auth)/reset-password'
    | '/(auth)/sign-in'
    | '/(auth)/sign-up'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/admin-login-activity'
    | '/_authenticated/contact-us'
    | '/_authenticated/'
    | '/_authenticated/announcements/affiliates'
    | '/_authenticated/announcements/moderators'
    | '/_authenticated/announcements/white-labels'
    | '/_authenticated/bot-messages/add'
    | '/_authenticated/flirt-messages/add'
    | '/_authenticated/gifs/add'
    | '/_authenticated/gifts/add'
    | '/_authenticated/models/create-model'
    | '/_authenticated/moderators/chat-group'
    | '/_authenticated/moderators/create-moderator'
    | '/_authenticated/moderators/domain'
    | '/_authenticated/moderators/login-activity'
    | '/_authenticated/moderators/reply-messages'
    | '/_authenticated/packages/add'
    | '/_authenticated/sessions/chat-mod'
    | '/_authenticated/sessions/loby'
    | '/_authenticated/settings/account'
    | '/_authenticated/settings/appearance'
    | '/_authenticated/settings/display'
    | '/_authenticated/settings/notifications'
    | '/_authenticated/smilies/add'
    | '/_authenticated/announcements/'
    | '/_authenticated/bot-messages/'
    | '/_authenticated/chats/'
    | '/_authenticated/currencies/'
    | '/_authenticated/flirt-messages/'
    | '/_authenticated/gifs/'
    | '/_authenticated/gifts/'
    | '/_authenticated/help-center/'
    | '/_authenticated/members/'
    | '/_authenticated/models/'
    | '/_authenticated/moderators/'
    | '/_authenticated/packages/'
    | '/_authenticated/sessions/'
    | '/_authenticated/settings/'
    | '/_authenticated/smilies/'
    | '/_authenticated/users/'
    | '/_authenticated/bot-messages/update/$msgId'
    | '/_authenticated/flirt-messages/update/$msgId'
    | '/_authenticated/gifs/update/$msgId'
    | '/_authenticated/gifts/update/$msgId'
    | '/_authenticated/members/profile/$memberId'
    | '/_authenticated/models/profile-images/$modelId'
    | '/_authenticated/models/profile/$modelId'
    | '/_authenticated/models/update-model/$modelId'
    | '/_authenticated/moderators/profile/$moderatorId'
    | '/_authenticated/moderators/update-moderator/$moderatorId'
    | '/_authenticated/packages/update/$msgId'
    | '/_authenticated/smilies/update/$msgId'
    | '/_authenticated/members/profile/$memberId/credits'
    | '/_authenticated/members/profile/$memberId/messages'
    | '/_authenticated/members/profile/$memberId/pictures'
    | '/_authenticated/members/profile/$memberId/messages/$conversationId'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  authForgotPasswordRoute: typeof authForgotPasswordRoute
  authOtpRoute: typeof authOtpRoute
  authResetPasswordRoute: typeof authResetPasswordRoute
  authSignInRoute: typeof authSignInRoute
  authSignUpRoute: typeof authSignUpRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/contact-us': {
      id: '/_authenticated/contact-us'
      path: '/contact-us'
      fullPath: '/contact-us'
      preLoaderRoute: typeof AuthenticatedContactUsRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/admin-login-activity': {
      id: '/_authenticated/admin-login-activity'
      path: '/admin-login-activity'
      fullPath: '/admin-login-activity'
      preLoaderRoute: typeof AuthenticatedAdminLoginActivityRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/sign-up': {
      id: '/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof authSignUpRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/reset-password': {
      id: '/(auth)/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof authResetPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/otp': {
      id: '/(auth)/otp'
      path: '/otp'
      fullPath: '/otp'
      preLoaderRoute: typeof authOtpRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/forgot-password': {
      id: '/(auth)/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof authForgotPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/smilies': {
      id: '/_authenticated/smilies'
      path: '/smilies'
      fullPath: '/smilies'
      preLoaderRoute: typeof AuthenticatedSmiliesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/sessions': {
      id: '/_authenticated/sessions'
      path: '/sessions'
      fullPath: '/sessions'
      preLoaderRoute: typeof AuthenticatedSessionsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/packages': {
      id: '/_authenticated/packages'
      path: '/packages'
      fullPath: '/packages'
      preLoaderRoute: typeof AuthenticatedPackagesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/moderators': {
      id: '/_authenticated/moderators'
      path: '/moderators'
      fullPath: '/moderators'
      preLoaderRoute: typeof AuthenticatedModeratorsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/models': {
      id: '/_authenticated/models'
      path: '/models'
      fullPath: '/models'
      preLoaderRoute: typeof AuthenticatedModelsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/members': {
      id: '/_authenticated/members'
      path: '/members'
      fullPath: '/members'
      preLoaderRoute: typeof AuthenticatedMembersRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/gifts': {
      id: '/_authenticated/gifts'
      path: '/gifts'
      fullPath: '/gifts'
      preLoaderRoute: typeof AuthenticatedGiftsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/gifs': {
      id: '/_authenticated/gifs'
      path: '/gifs'
      fullPath: '/gifs'
      preLoaderRoute: typeof AuthenticatedGifsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/flirt-messages': {
      id: '/_authenticated/flirt-messages'
      path: '/flirt-messages'
      fullPath: '/flirt-messages'
      preLoaderRoute: typeof AuthenticatedFlirtMessagesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/currencies': {
      id: '/_authenticated/currencies'
      path: '/currencies'
      fullPath: '/currencies'
      preLoaderRoute: typeof AuthenticatedCurrenciesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/bot-messages': {
      id: '/_authenticated/bot-messages'
      path: '/bot-messages'
      fullPath: '/bot-messages'
      preLoaderRoute: typeof AuthenticatedBotMessagesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/announcements': {
      id: '/_authenticated/announcements'
      path: '/announcements'
      fullPath: '/announcements'
      preLoaderRoute: typeof AuthenticatedAnnouncementsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/users/': {
      id: '/_authenticated/users/'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof AuthenticatedUsersIndexRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/smilies/': {
      id: '/_authenticated/smilies/'
      path: '/'
      fullPath: '/smilies/'
      preLoaderRoute: typeof AuthenticatedSmiliesIndexRouteImport
      parentRoute: typeof AuthenticatedSmiliesRouteRoute
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof AuthenticatedSettingsIndexRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/sessions/': {
      id: '/_authenticated/sessions/'
      path: '/'
      fullPath: '/sessions/'
      preLoaderRoute: typeof AuthenticatedSessionsIndexRouteImport
      parentRoute: typeof AuthenticatedSessionsRouteRoute
    }
    '/_authenticated/packages/': {
      id: '/_authenticated/packages/'
      path: '/'
      fullPath: '/packages/'
      preLoaderRoute: typeof AuthenticatedPackagesIndexRouteImport
      parentRoute: typeof AuthenticatedPackagesRouteRoute
    }
    '/_authenticated/moderators/': {
      id: '/_authenticated/moderators/'
      path: '/'
      fullPath: '/moderators/'
      preLoaderRoute: typeof AuthenticatedModeratorsIndexRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/models/': {
      id: '/_authenticated/models/'
      path: '/'
      fullPath: '/models/'
      preLoaderRoute: typeof AuthenticatedModelsIndexRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/members/': {
      id: '/_authenticated/members/'
      path: '/'
      fullPath: '/members/'
      preLoaderRoute: typeof AuthenticatedMembersIndexRouteImport
      parentRoute: typeof AuthenticatedMembersRouteRoute
    }
    '/_authenticated/help-center/': {
      id: '/_authenticated/help-center/'
      path: '/help-center'
      fullPath: '/help-center'
      preLoaderRoute: typeof AuthenticatedHelpCenterIndexRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/gifts/': {
      id: '/_authenticated/gifts/'
      path: '/'
      fullPath: '/gifts/'
      preLoaderRoute: typeof AuthenticatedGiftsIndexRouteImport
      parentRoute: typeof AuthenticatedGiftsRouteRoute
    }
    '/_authenticated/gifs/': {
      id: '/_authenticated/gifs/'
      path: '/'
      fullPath: '/gifs/'
      preLoaderRoute: typeof AuthenticatedGifsIndexRouteImport
      parentRoute: typeof AuthenticatedGifsRouteRoute
    }
    '/_authenticated/flirt-messages/': {
      id: '/_authenticated/flirt-messages/'
      path: '/'
      fullPath: '/flirt-messages/'
      preLoaderRoute: typeof AuthenticatedFlirtMessagesIndexRouteImport
      parentRoute: typeof AuthenticatedFlirtMessagesRouteRoute
    }
    '/_authenticated/currencies/': {
      id: '/_authenticated/currencies/'
      path: '/'
      fullPath: '/currencies/'
      preLoaderRoute: typeof AuthenticatedCurrenciesIndexRouteImport
      parentRoute: typeof AuthenticatedCurrenciesRouteRoute
    }
    '/_authenticated/chats/': {
      id: '/_authenticated/chats/'
      path: '/chats'
      fullPath: '/chats'
      preLoaderRoute: typeof AuthenticatedChatsIndexRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/bot-messages/': {
      id: '/_authenticated/bot-messages/'
      path: '/'
      fullPath: '/bot-messages/'
      preLoaderRoute: typeof AuthenticatedBotMessagesIndexRouteImport
      parentRoute: typeof AuthenticatedBotMessagesRouteRoute
    }
    '/_authenticated/announcements/': {
      id: '/_authenticated/announcements/'
      path: '/'
      fullPath: '/announcements/'
      preLoaderRoute: typeof AuthenticatedAnnouncementsIndexRouteImport
      parentRoute: typeof AuthenticatedAnnouncementsRouteRoute
    }
    '/_authenticated/smilies/add': {
      id: '/_authenticated/smilies/add'
      path: '/add'
      fullPath: '/smilies/add'
      preLoaderRoute: typeof AuthenticatedSmiliesAddRouteImport
      parentRoute: typeof AuthenticatedSmiliesRouteRoute
    }
    '/_authenticated/settings/notifications': {
      id: '/_authenticated/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationsRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/display': {
      id: '/_authenticated/settings/display'
      path: '/display'
      fullPath: '/settings/display'
      preLoaderRoute: typeof AuthenticatedSettingsDisplayRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/appearance': {
      id: '/_authenticated/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof AuthenticatedSettingsAppearanceRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/account': {
      id: '/_authenticated/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof AuthenticatedSettingsAccountRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/sessions/loby': {
      id: '/_authenticated/sessions/loby'
      path: '/loby'
      fullPath: '/sessions/loby'
      preLoaderRoute: typeof AuthenticatedSessionsLobyRouteImport
      parentRoute: typeof AuthenticatedSessionsRouteRoute
    }
    '/_authenticated/sessions/chat-mod': {
      id: '/_authenticated/sessions/chat-mod'
      path: '/chat-mod'
      fullPath: '/sessions/chat-mod'
      preLoaderRoute: typeof AuthenticatedSessionsChatModRouteImport
      parentRoute: typeof AuthenticatedSessionsRouteRoute
    }
    '/_authenticated/packages/add': {
      id: '/_authenticated/packages/add'
      path: '/add'
      fullPath: '/packages/add'
      preLoaderRoute: typeof AuthenticatedPackagesAddRouteImport
      parentRoute: typeof AuthenticatedPackagesRouteRoute
    }
    '/_authenticated/moderators/reply-messages': {
      id: '/_authenticated/moderators/reply-messages'
      path: '/reply-messages'
      fullPath: '/moderators/reply-messages'
      preLoaderRoute: typeof AuthenticatedModeratorsReplyMessagesRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/login-activity': {
      id: '/_authenticated/moderators/login-activity'
      path: '/login-activity'
      fullPath: '/moderators/login-activity'
      preLoaderRoute: typeof AuthenticatedModeratorsLoginActivityRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/domain': {
      id: '/_authenticated/moderators/domain'
      path: '/domain'
      fullPath: '/moderators/domain'
      preLoaderRoute: typeof AuthenticatedModeratorsDomainRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/create-moderator': {
      id: '/_authenticated/moderators/create-moderator'
      path: '/create-moderator'
      fullPath: '/moderators/create-moderator'
      preLoaderRoute: typeof AuthenticatedModeratorsCreateModeratorRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/chat-group': {
      id: '/_authenticated/moderators/chat-group'
      path: '/chat-group'
      fullPath: '/moderators/chat-group'
      preLoaderRoute: typeof AuthenticatedModeratorsChatGroupRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/models/create-model': {
      id: '/_authenticated/models/create-model'
      path: '/create-model'
      fullPath: '/models/create-model'
      preLoaderRoute: typeof AuthenticatedModelsCreateModelRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/gifts/add': {
      id: '/_authenticated/gifts/add'
      path: '/add'
      fullPath: '/gifts/add'
      preLoaderRoute: typeof AuthenticatedGiftsAddRouteImport
      parentRoute: typeof AuthenticatedGiftsRouteRoute
    }
    '/_authenticated/gifs/add': {
      id: '/_authenticated/gifs/add'
      path: '/add'
      fullPath: '/gifs/add'
      preLoaderRoute: typeof AuthenticatedGifsAddRouteImport
      parentRoute: typeof AuthenticatedGifsRouteRoute
    }
    '/_authenticated/flirt-messages/add': {
      id: '/_authenticated/flirt-messages/add'
      path: '/add'
      fullPath: '/flirt-messages/add'
      preLoaderRoute: typeof AuthenticatedFlirtMessagesAddRouteImport
      parentRoute: typeof AuthenticatedFlirtMessagesRouteRoute
    }
    '/_authenticated/bot-messages/add': {
      id: '/_authenticated/bot-messages/add'
      path: '/add'
      fullPath: '/bot-messages/add'
      preLoaderRoute: typeof AuthenticatedBotMessagesAddRouteImport
      parentRoute: typeof AuthenticatedBotMessagesRouteRoute
    }
    '/_authenticated/announcements/white-labels': {
      id: '/_authenticated/announcements/white-labels'
      path: '/white-labels'
      fullPath: '/announcements/white-labels'
      preLoaderRoute: typeof AuthenticatedAnnouncementsWhiteLabelsRouteImport
      parentRoute: typeof AuthenticatedAnnouncementsRouteRoute
    }
    '/_authenticated/announcements/moderators': {
      id: '/_authenticated/announcements/moderators'
      path: '/moderators'
      fullPath: '/announcements/moderators'
      preLoaderRoute: typeof AuthenticatedAnnouncementsModeratorsRouteImport
      parentRoute: typeof AuthenticatedAnnouncementsRouteRoute
    }
    '/_authenticated/announcements/affiliates': {
      id: '/_authenticated/announcements/affiliates'
      path: '/affiliates'
      fullPath: '/announcements/affiliates'
      preLoaderRoute: typeof AuthenticatedAnnouncementsAffiliatesRouteImport
      parentRoute: typeof AuthenticatedAnnouncementsRouteRoute
    }
    '/_authenticated/smilies/update/$msgId': {
      id: '/_authenticated/smilies/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/smilies/update/$msgId'
      preLoaderRoute: typeof AuthenticatedSmiliesUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedSmiliesRouteRoute
    }
    '/_authenticated/packages/update/$msgId': {
      id: '/_authenticated/packages/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/packages/update/$msgId'
      preLoaderRoute: typeof AuthenticatedPackagesUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedPackagesRouteRoute
    }
    '/_authenticated/moderators/update-moderator/$moderatorId': {
      id: '/_authenticated/moderators/update-moderator/$moderatorId'
      path: '/update-moderator/$moderatorId'
      fullPath: '/moderators/update-moderator/$moderatorId'
      preLoaderRoute: typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/profile/$moderatorId': {
      id: '/_authenticated/moderators/profile/$moderatorId'
      path: '/profile/$moderatorId'
      fullPath: '/moderators/profile/$moderatorId'
      preLoaderRoute: typeof AuthenticatedModeratorsProfileModeratorIdRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/models/update-model/$modelId': {
      id: '/_authenticated/models/update-model/$modelId'
      path: '/update-model/$modelId'
      fullPath: '/models/update-model/$modelId'
      preLoaderRoute: typeof AuthenticatedModelsUpdateModelModelIdRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/models/profile/$modelId': {
      id: '/_authenticated/models/profile/$modelId'
      path: '/profile/$modelId'
      fullPath: '/models/profile/$modelId'
      preLoaderRoute: typeof AuthenticatedModelsProfileModelIdRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/models/profile-images/$modelId': {
      id: '/_authenticated/models/profile-images/$modelId'
      path: '/profile-images/$modelId'
      fullPath: '/models/profile-images/$modelId'
      preLoaderRoute: typeof AuthenticatedModelsProfileImagesModelIdRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/members/profile/$memberId': {
      id: '/_authenticated/members/profile/$memberId'
      path: '/profile/$memberId'
      fullPath: '/members/profile/$memberId'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdRouteImport
      parentRoute: typeof AuthenticatedMembersRouteRoute
    }
    '/_authenticated/gifts/update/$msgId': {
      id: '/_authenticated/gifts/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/gifts/update/$msgId'
      preLoaderRoute: typeof AuthenticatedGiftsUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedGiftsRouteRoute
    }
    '/_authenticated/gifs/update/$msgId': {
      id: '/_authenticated/gifs/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/gifs/update/$msgId'
      preLoaderRoute: typeof AuthenticatedGifsUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedGifsRouteRoute
    }
    '/_authenticated/flirt-messages/update/$msgId': {
      id: '/_authenticated/flirt-messages/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/flirt-messages/update/$msgId'
      preLoaderRoute: typeof AuthenticatedFlirtMessagesUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedFlirtMessagesRouteRoute
    }
    '/_authenticated/bot-messages/update/$msgId': {
      id: '/_authenticated/bot-messages/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/bot-messages/update/$msgId'
      preLoaderRoute: typeof AuthenticatedBotMessagesUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedBotMessagesRouteRoute
    }
    '/_authenticated/members/profile/$memberId/pictures': {
      id: '/_authenticated/members/profile/$memberId/pictures'
      path: '/pictures'
      fullPath: '/members/profile/$memberId/pictures'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdPicturesRouteImport
      parentRoute: typeof AuthenticatedMembersProfileMemberIdRoute
    }
    '/_authenticated/members/profile/$memberId/messages': {
      id: '/_authenticated/members/profile/$memberId/messages'
      path: '/messages'
      fullPath: '/members/profile/$memberId/messages'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdMessagesRouteImport
      parentRoute: typeof AuthenticatedMembersProfileMemberIdRoute
    }
    '/_authenticated/members/profile/$memberId/credits': {
      id: '/_authenticated/members/profile/$memberId/credits'
      path: '/credits'
      fullPath: '/members/profile/$memberId/credits'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdCreditsRouteImport
      parentRoute: typeof AuthenticatedMembersProfileMemberIdRoute
    }
    '/_authenticated/members/profile/$memberId/messages/$conversationId': {
      id: '/_authenticated/members/profile/$memberId/messages/$conversationId'
      path: '/$conversationId'
      fullPath: '/members/profile/$memberId/messages/$conversationId'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRouteImport
      parentRoute: typeof AuthenticatedMembersProfileMemberIdMessagesRoute
    }
  }
}

interface AuthenticatedAnnouncementsRouteRouteChildren {
  AuthenticatedAnnouncementsAffiliatesRoute: typeof AuthenticatedAnnouncementsAffiliatesRoute
  AuthenticatedAnnouncementsModeratorsRoute: typeof AuthenticatedAnnouncementsModeratorsRoute
  AuthenticatedAnnouncementsWhiteLabelsRoute: typeof AuthenticatedAnnouncementsWhiteLabelsRoute
  AuthenticatedAnnouncementsIndexRoute: typeof AuthenticatedAnnouncementsIndexRoute
}

const AuthenticatedAnnouncementsRouteRouteChildren: AuthenticatedAnnouncementsRouteRouteChildren =
  {
    AuthenticatedAnnouncementsAffiliatesRoute:
      AuthenticatedAnnouncementsAffiliatesRoute,
    AuthenticatedAnnouncementsModeratorsRoute:
      AuthenticatedAnnouncementsModeratorsRoute,
    AuthenticatedAnnouncementsWhiteLabelsRoute:
      AuthenticatedAnnouncementsWhiteLabelsRoute,
    AuthenticatedAnnouncementsIndexRoute: AuthenticatedAnnouncementsIndexRoute,
  }

const AuthenticatedAnnouncementsRouteRouteWithChildren =
  AuthenticatedAnnouncementsRouteRoute._addFileChildren(
    AuthenticatedAnnouncementsRouteRouteChildren,
  )

interface AuthenticatedBotMessagesRouteRouteChildren {
  AuthenticatedBotMessagesAddRoute: typeof AuthenticatedBotMessagesAddRoute
  AuthenticatedBotMessagesIndexRoute: typeof AuthenticatedBotMessagesIndexRoute
  AuthenticatedBotMessagesUpdateMsgIdRoute: typeof AuthenticatedBotMessagesUpdateMsgIdRoute
}

const AuthenticatedBotMessagesRouteRouteChildren: AuthenticatedBotMessagesRouteRouteChildren =
  {
    AuthenticatedBotMessagesAddRoute: AuthenticatedBotMessagesAddRoute,
    AuthenticatedBotMessagesIndexRoute: AuthenticatedBotMessagesIndexRoute,
    AuthenticatedBotMessagesUpdateMsgIdRoute:
      AuthenticatedBotMessagesUpdateMsgIdRoute,
  }

const AuthenticatedBotMessagesRouteRouteWithChildren =
  AuthenticatedBotMessagesRouteRoute._addFileChildren(
    AuthenticatedBotMessagesRouteRouteChildren,
  )

interface AuthenticatedCurrenciesRouteRouteChildren {
  AuthenticatedCurrenciesIndexRoute: typeof AuthenticatedCurrenciesIndexRoute
}

const AuthenticatedCurrenciesRouteRouteChildren: AuthenticatedCurrenciesRouteRouteChildren =
  {
    AuthenticatedCurrenciesIndexRoute: AuthenticatedCurrenciesIndexRoute,
  }

const AuthenticatedCurrenciesRouteRouteWithChildren =
  AuthenticatedCurrenciesRouteRoute._addFileChildren(
    AuthenticatedCurrenciesRouteRouteChildren,
  )

interface AuthenticatedFlirtMessagesRouteRouteChildren {
  AuthenticatedFlirtMessagesAddRoute: typeof AuthenticatedFlirtMessagesAddRoute
  AuthenticatedFlirtMessagesIndexRoute: typeof AuthenticatedFlirtMessagesIndexRoute
  AuthenticatedFlirtMessagesUpdateMsgIdRoute: typeof AuthenticatedFlirtMessagesUpdateMsgIdRoute
}

const AuthenticatedFlirtMessagesRouteRouteChildren: AuthenticatedFlirtMessagesRouteRouteChildren =
  {
    AuthenticatedFlirtMessagesAddRoute: AuthenticatedFlirtMessagesAddRoute,
    AuthenticatedFlirtMessagesIndexRoute: AuthenticatedFlirtMessagesIndexRoute,
    AuthenticatedFlirtMessagesUpdateMsgIdRoute:
      AuthenticatedFlirtMessagesUpdateMsgIdRoute,
  }

const AuthenticatedFlirtMessagesRouteRouteWithChildren =
  AuthenticatedFlirtMessagesRouteRoute._addFileChildren(
    AuthenticatedFlirtMessagesRouteRouteChildren,
  )

interface AuthenticatedGifsRouteRouteChildren {
  AuthenticatedGifsAddRoute: typeof AuthenticatedGifsAddRoute
  AuthenticatedGifsIndexRoute: typeof AuthenticatedGifsIndexRoute
  AuthenticatedGifsUpdateMsgIdRoute: typeof AuthenticatedGifsUpdateMsgIdRoute
}

const AuthenticatedGifsRouteRouteChildren: AuthenticatedGifsRouteRouteChildren =
  {
    AuthenticatedGifsAddRoute: AuthenticatedGifsAddRoute,
    AuthenticatedGifsIndexRoute: AuthenticatedGifsIndexRoute,
    AuthenticatedGifsUpdateMsgIdRoute: AuthenticatedGifsUpdateMsgIdRoute,
  }

const AuthenticatedGifsRouteRouteWithChildren =
  AuthenticatedGifsRouteRoute._addFileChildren(
    AuthenticatedGifsRouteRouteChildren,
  )

interface AuthenticatedGiftsRouteRouteChildren {
  AuthenticatedGiftsAddRoute: typeof AuthenticatedGiftsAddRoute
  AuthenticatedGiftsIndexRoute: typeof AuthenticatedGiftsIndexRoute
  AuthenticatedGiftsUpdateMsgIdRoute: typeof AuthenticatedGiftsUpdateMsgIdRoute
}

const AuthenticatedGiftsRouteRouteChildren: AuthenticatedGiftsRouteRouteChildren =
  {
    AuthenticatedGiftsAddRoute: AuthenticatedGiftsAddRoute,
    AuthenticatedGiftsIndexRoute: AuthenticatedGiftsIndexRoute,
    AuthenticatedGiftsUpdateMsgIdRoute: AuthenticatedGiftsUpdateMsgIdRoute,
  }

const AuthenticatedGiftsRouteRouteWithChildren =
  AuthenticatedGiftsRouteRoute._addFileChildren(
    AuthenticatedGiftsRouteRouteChildren,
  )

interface AuthenticatedMembersProfileMemberIdMessagesRouteChildren {
  AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute: typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute
}

const AuthenticatedMembersProfileMemberIdMessagesRouteChildren: AuthenticatedMembersProfileMemberIdMessagesRouteChildren =
  {
    AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute:
      AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute,
  }

const AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren =
  AuthenticatedMembersProfileMemberIdMessagesRoute._addFileChildren(
    AuthenticatedMembersProfileMemberIdMessagesRouteChildren,
  )

interface AuthenticatedMembersProfileMemberIdRouteChildren {
  AuthenticatedMembersProfileMemberIdCreditsRoute: typeof AuthenticatedMembersProfileMemberIdCreditsRoute
  AuthenticatedMembersProfileMemberIdMessagesRoute: typeof AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren
  AuthenticatedMembersProfileMemberIdPicturesRoute: typeof AuthenticatedMembersProfileMemberIdPicturesRoute
}

const AuthenticatedMembersProfileMemberIdRouteChildren: AuthenticatedMembersProfileMemberIdRouteChildren =
  {
    AuthenticatedMembersProfileMemberIdCreditsRoute:
      AuthenticatedMembersProfileMemberIdCreditsRoute,
    AuthenticatedMembersProfileMemberIdMessagesRoute:
      AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren,
    AuthenticatedMembersProfileMemberIdPicturesRoute:
      AuthenticatedMembersProfileMemberIdPicturesRoute,
  }

const AuthenticatedMembersProfileMemberIdRouteWithChildren =
  AuthenticatedMembersProfileMemberIdRoute._addFileChildren(
    AuthenticatedMembersProfileMemberIdRouteChildren,
  )

interface AuthenticatedMembersRouteRouteChildren {
  AuthenticatedMembersIndexRoute: typeof AuthenticatedMembersIndexRoute
  AuthenticatedMembersProfileMemberIdRoute: typeof AuthenticatedMembersProfileMemberIdRouteWithChildren
}

const AuthenticatedMembersRouteRouteChildren: AuthenticatedMembersRouteRouteChildren =
  {
    AuthenticatedMembersIndexRoute: AuthenticatedMembersIndexRoute,
    AuthenticatedMembersProfileMemberIdRoute:
      AuthenticatedMembersProfileMemberIdRouteWithChildren,
  }

const AuthenticatedMembersRouteRouteWithChildren =
  AuthenticatedMembersRouteRoute._addFileChildren(
    AuthenticatedMembersRouteRouteChildren,
  )

interface AuthenticatedModelsRouteRouteChildren {
  AuthenticatedModelsCreateModelRoute: typeof AuthenticatedModelsCreateModelRoute
  AuthenticatedModelsIndexRoute: typeof AuthenticatedModelsIndexRoute
  AuthenticatedModelsProfileImagesModelIdRoute: typeof AuthenticatedModelsProfileImagesModelIdRoute
  AuthenticatedModelsProfileModelIdRoute: typeof AuthenticatedModelsProfileModelIdRoute
  AuthenticatedModelsUpdateModelModelIdRoute: typeof AuthenticatedModelsUpdateModelModelIdRoute
}

const AuthenticatedModelsRouteRouteChildren: AuthenticatedModelsRouteRouteChildren =
  {
    AuthenticatedModelsCreateModelRoute: AuthenticatedModelsCreateModelRoute,
    AuthenticatedModelsIndexRoute: AuthenticatedModelsIndexRoute,
    AuthenticatedModelsProfileImagesModelIdRoute:
      AuthenticatedModelsProfileImagesModelIdRoute,
    AuthenticatedModelsProfileModelIdRoute:
      AuthenticatedModelsProfileModelIdRoute,
    AuthenticatedModelsUpdateModelModelIdRoute:
      AuthenticatedModelsUpdateModelModelIdRoute,
  }

const AuthenticatedModelsRouteRouteWithChildren =
  AuthenticatedModelsRouteRoute._addFileChildren(
    AuthenticatedModelsRouteRouteChildren,
  )

interface AuthenticatedModeratorsRouteRouteChildren {
  AuthenticatedModeratorsChatGroupRoute: typeof AuthenticatedModeratorsChatGroupRoute
  AuthenticatedModeratorsCreateModeratorRoute: typeof AuthenticatedModeratorsCreateModeratorRoute
  AuthenticatedModeratorsDomainRoute: typeof AuthenticatedModeratorsDomainRoute
  AuthenticatedModeratorsLoginActivityRoute: typeof AuthenticatedModeratorsLoginActivityRoute
  AuthenticatedModeratorsReplyMessagesRoute: typeof AuthenticatedModeratorsReplyMessagesRoute
  AuthenticatedModeratorsIndexRoute: typeof AuthenticatedModeratorsIndexRoute
  AuthenticatedModeratorsProfileModeratorIdRoute: typeof AuthenticatedModeratorsProfileModeratorIdRoute
  AuthenticatedModeratorsUpdateModeratorModeratorIdRoute: typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRoute
}

const AuthenticatedModeratorsRouteRouteChildren: AuthenticatedModeratorsRouteRouteChildren =
  {
    AuthenticatedModeratorsChatGroupRoute:
      AuthenticatedModeratorsChatGroupRoute,
    AuthenticatedModeratorsCreateModeratorRoute:
      AuthenticatedModeratorsCreateModeratorRoute,
    AuthenticatedModeratorsDomainRoute: AuthenticatedModeratorsDomainRoute,
    AuthenticatedModeratorsLoginActivityRoute:
      AuthenticatedModeratorsLoginActivityRoute,
    AuthenticatedModeratorsReplyMessagesRoute:
      AuthenticatedModeratorsReplyMessagesRoute,
    AuthenticatedModeratorsIndexRoute: AuthenticatedModeratorsIndexRoute,
    AuthenticatedModeratorsProfileModeratorIdRoute:
      AuthenticatedModeratorsProfileModeratorIdRoute,
    AuthenticatedModeratorsUpdateModeratorModeratorIdRoute:
      AuthenticatedModeratorsUpdateModeratorModeratorIdRoute,
  }

const AuthenticatedModeratorsRouteRouteWithChildren =
  AuthenticatedModeratorsRouteRoute._addFileChildren(
    AuthenticatedModeratorsRouteRouteChildren,
  )

interface AuthenticatedPackagesRouteRouteChildren {
  AuthenticatedPackagesAddRoute: typeof AuthenticatedPackagesAddRoute
  AuthenticatedPackagesIndexRoute: typeof AuthenticatedPackagesIndexRoute
  AuthenticatedPackagesUpdateMsgIdRoute: typeof AuthenticatedPackagesUpdateMsgIdRoute
}

const AuthenticatedPackagesRouteRouteChildren: AuthenticatedPackagesRouteRouteChildren =
  {
    AuthenticatedPackagesAddRoute: AuthenticatedPackagesAddRoute,
    AuthenticatedPackagesIndexRoute: AuthenticatedPackagesIndexRoute,
    AuthenticatedPackagesUpdateMsgIdRoute:
      AuthenticatedPackagesUpdateMsgIdRoute,
  }

const AuthenticatedPackagesRouteRouteWithChildren =
  AuthenticatedPackagesRouteRoute._addFileChildren(
    AuthenticatedPackagesRouteRouteChildren,
  )

interface AuthenticatedSessionsRouteRouteChildren {
  AuthenticatedSessionsChatModRoute: typeof AuthenticatedSessionsChatModRoute
  AuthenticatedSessionsLobyRoute: typeof AuthenticatedSessionsLobyRoute
  AuthenticatedSessionsIndexRoute: typeof AuthenticatedSessionsIndexRoute
}

const AuthenticatedSessionsRouteRouteChildren: AuthenticatedSessionsRouteRouteChildren =
  {
    AuthenticatedSessionsChatModRoute: AuthenticatedSessionsChatModRoute,
    AuthenticatedSessionsLobyRoute: AuthenticatedSessionsLobyRoute,
    AuthenticatedSessionsIndexRoute: AuthenticatedSessionsIndexRoute,
  }

const AuthenticatedSessionsRouteRouteWithChildren =
  AuthenticatedSessionsRouteRoute._addFileChildren(
    AuthenticatedSessionsRouteRouteChildren,
  )

interface AuthenticatedSettingsRouteRouteChildren {
  AuthenticatedSettingsAccountRoute: typeof AuthenticatedSettingsAccountRoute
  AuthenticatedSettingsAppearanceRoute: typeof AuthenticatedSettingsAppearanceRoute
  AuthenticatedSettingsDisplayRoute: typeof AuthenticatedSettingsDisplayRoute
  AuthenticatedSettingsNotificationsRoute: typeof AuthenticatedSettingsNotificationsRoute
  AuthenticatedSettingsIndexRoute: typeof AuthenticatedSettingsIndexRoute
}

const AuthenticatedSettingsRouteRouteChildren: AuthenticatedSettingsRouteRouteChildren =
  {
    AuthenticatedSettingsAccountRoute: AuthenticatedSettingsAccountRoute,
    AuthenticatedSettingsAppearanceRoute: AuthenticatedSettingsAppearanceRoute,
    AuthenticatedSettingsDisplayRoute: AuthenticatedSettingsDisplayRoute,
    AuthenticatedSettingsNotificationsRoute:
      AuthenticatedSettingsNotificationsRoute,
    AuthenticatedSettingsIndexRoute: AuthenticatedSettingsIndexRoute,
  }

const AuthenticatedSettingsRouteRouteWithChildren =
  AuthenticatedSettingsRouteRoute._addFileChildren(
    AuthenticatedSettingsRouteRouteChildren,
  )

interface AuthenticatedSmiliesRouteRouteChildren {
  AuthenticatedSmiliesAddRoute: typeof AuthenticatedSmiliesAddRoute
  AuthenticatedSmiliesIndexRoute: typeof AuthenticatedSmiliesIndexRoute
  AuthenticatedSmiliesUpdateMsgIdRoute: typeof AuthenticatedSmiliesUpdateMsgIdRoute
}

const AuthenticatedSmiliesRouteRouteChildren: AuthenticatedSmiliesRouteRouteChildren =
  {
    AuthenticatedSmiliesAddRoute: AuthenticatedSmiliesAddRoute,
    AuthenticatedSmiliesIndexRoute: AuthenticatedSmiliesIndexRoute,
    AuthenticatedSmiliesUpdateMsgIdRoute: AuthenticatedSmiliesUpdateMsgIdRoute,
  }

const AuthenticatedSmiliesRouteRouteWithChildren =
  AuthenticatedSmiliesRouteRoute._addFileChildren(
    AuthenticatedSmiliesRouteRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedAnnouncementsRouteRoute: typeof AuthenticatedAnnouncementsRouteRouteWithChildren
  AuthenticatedBotMessagesRouteRoute: typeof AuthenticatedBotMessagesRouteRouteWithChildren
  AuthenticatedCurrenciesRouteRoute: typeof AuthenticatedCurrenciesRouteRouteWithChildren
  AuthenticatedFlirtMessagesRouteRoute: typeof AuthenticatedFlirtMessagesRouteRouteWithChildren
  AuthenticatedGifsRouteRoute: typeof AuthenticatedGifsRouteRouteWithChildren
  AuthenticatedGiftsRouteRoute: typeof AuthenticatedGiftsRouteRouteWithChildren
  AuthenticatedMembersRouteRoute: typeof AuthenticatedMembersRouteRouteWithChildren
  AuthenticatedModelsRouteRoute: typeof AuthenticatedModelsRouteRouteWithChildren
  AuthenticatedModeratorsRouteRoute: typeof AuthenticatedModeratorsRouteRouteWithChildren
  AuthenticatedPackagesRouteRoute: typeof AuthenticatedPackagesRouteRouteWithChildren
  AuthenticatedSessionsRouteRoute: typeof AuthenticatedSessionsRouteRouteWithChildren
  AuthenticatedSettingsRouteRoute: typeof AuthenticatedSettingsRouteRouteWithChildren
  AuthenticatedSmiliesRouteRoute: typeof AuthenticatedSmiliesRouteRouteWithChildren
  AuthenticatedAdminLoginActivityRoute: typeof AuthenticatedAdminLoginActivityRoute
  AuthenticatedContactUsRoute: typeof AuthenticatedContactUsRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedChatsIndexRoute: typeof AuthenticatedChatsIndexRoute
  AuthenticatedHelpCenterIndexRoute: typeof AuthenticatedHelpCenterIndexRoute
  AuthenticatedUsersIndexRoute: typeof AuthenticatedUsersIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedAnnouncementsRouteRoute:
    AuthenticatedAnnouncementsRouteRouteWithChildren,
  AuthenticatedBotMessagesRouteRoute:
    AuthenticatedBotMessagesRouteRouteWithChildren,
  AuthenticatedCurrenciesRouteRoute:
    AuthenticatedCurrenciesRouteRouteWithChildren,
  AuthenticatedFlirtMessagesRouteRoute:
    AuthenticatedFlirtMessagesRouteRouteWithChildren,
  AuthenticatedGifsRouteRoute: AuthenticatedGifsRouteRouteWithChildren,
  AuthenticatedGiftsRouteRoute: AuthenticatedGiftsRouteRouteWithChildren,
  AuthenticatedMembersRouteRoute: AuthenticatedMembersRouteRouteWithChildren,
  AuthenticatedModelsRouteRoute: AuthenticatedModelsRouteRouteWithChildren,
  AuthenticatedModeratorsRouteRoute:
    AuthenticatedModeratorsRouteRouteWithChildren,
  AuthenticatedPackagesRouteRoute: AuthenticatedPackagesRouteRouteWithChildren,
  AuthenticatedSessionsRouteRoute: AuthenticatedSessionsRouteRouteWithChildren,
  AuthenticatedSettingsRouteRoute: AuthenticatedSettingsRouteRouteWithChildren,
  AuthenticatedSmiliesRouteRoute: AuthenticatedSmiliesRouteRouteWithChildren,
  AuthenticatedAdminLoginActivityRoute: AuthenticatedAdminLoginActivityRoute,
  AuthenticatedContactUsRoute: AuthenticatedContactUsRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedChatsIndexRoute: AuthenticatedChatsIndexRoute,
  AuthenticatedHelpCenterIndexRoute: AuthenticatedHelpCenterIndexRoute,
  AuthenticatedUsersIndexRoute: AuthenticatedUsersIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  authForgotPasswordRoute: authForgotPasswordRoute,
  authOtpRoute: authOtpRoute,
  authResetPasswordRoute: authResetPasswordRoute,
  authSignInRoute: authSignInRoute,
  authSignUpRoute: authSignUpRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
