import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import Cookies from 'js-cookie'

const ACCESS_TOKEN = 'ACCESS_TOKEN'

interface AuthUser {
  accountNo: string
  email: string
  role: string[]
  exp: number
}

interface AuthSlice {
  user: AuthUser | null
  accessToken: string
  setUser: (user: AuthUser | null) => void
  setAccessToken: (accessToken: string) => void
  resetAccessToken: () => void
  reset: () => void
}

interface UIState {
  showSpinner: boolean
  setShowSpinner: (show: boolean) => void
}

type StoreState = {
  auth: AuthSlice
  ui: UIState
}

export const useAuthStore = create<StoreState>()(
  persist(
    (set, get) => ({
      auth: {
        user: null,
        accessToken: Cookies.get(ACCESS_TOKEN) ?? '',

        setUser: (user) =>
          set((state) => ({
            auth: { ...state.auth, user },
          })),

        setAccessToken: (accessToken) => {
          Cookies.set(ACCESS_TOKEN, accessToken)
          set((state) => ({
            auth: { ...state.auth, accessToken },
          }))
        },

        resetAccessToken: () => {
          Cookies.remove(ACCESS_TOKEN)
          set((state) => ({
            auth: { ...state.auth, accessToken: '' },
          }))
        },

        reset: () => {
          Cookies.remove(ACCESS_TOKEN)
          localStorage.removeItem('rememberedEmail')
          localStorage.removeItem('rememberMe')
          set((state) => ({
            auth: {
              ...state.auth,
              user: null,
              accessToken: '',
            },
          }))
        },
      },

      ui: {
        showSpinner: false,
        setShowSpinner: (show) =>
          set((state) => ({
            ui: { ...state.ui, showSpinner: show },
          })),
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        // Flatten values for persist, not whole auth
        auth: {
          user: state.auth.user,
          accessToken: state.auth.accessToken,
        },
      }),
      merge: (persistedState, currentState) => ({
        ...currentState,
        auth: {
          ...currentState.auth,
          ...(persistedState as any)?.auth,
        },
      }),
    }
  )
)

export const getAccessToken = () => {
  const token = Cookies.get(ACCESS_TOKEN)
  return token ?? ''
}
