/**
 * Utility functions for testing role-based navigation
 * This file can be used for debugging and testing role-based access
 */

import { sidebarData } from '@/components/layout/data/sidebar-data';
import { getFilteredSidebarData } from './role-based-navigation';

// Common role combinations for testing
export const TEST_ROLES = {
  SUPERADMIN: ['superadmin'],
  ADMIN: ['admin'],
  MANAGER: ['manager'],
  CASHIER: ['cashier'],
  CHAT_MOD: ['chat-mod'],
  ADMIN_MANAGER: ['admin', 'manager'],
  ALL_ROLES: ['superadmin', 'admin', 'manager', 'cashier', 'chat-mod'],
  NO_ROLES: [],
} as const;

/**
 * Test function to see what navigation items are visible for different roles
 * @param roles - Array of roles to test
 * @returns Object with navigation structure and item counts
 */
export function testRoleAccess(roles: string[]) {
  const filteredData = getFilteredSidebarData(sidebarData, roles);
  
  const result = {
    roles,
    totalGroups: filteredData.navGroups.length,
    groups: filteredData.navGroups.map(group => ({
      title: group.title,
      itemCount: group.items.length,
      items: group.items.map(item => ({
        title: item.title,
        hasUrl: !!item.url,
        hasSubItems: !!(item.items && item.items.length > 0),
        subItemCount: item.items ? item.items.length : 0,
        requiredRoles: item.roles || [],
        subItems: item.items ? item.items.map(subItem => ({
          title: subItem.title,
          hasUrl: !!subItem.url,
          requiredRoles: subItem.roles || [],
          hasSubItems: !!(subItem.items && subItem.items.length > 0),
          subItemCount: subItem.items ? subItem.items.length : 0,
        })) : []
      }))
    }))
  };
  
  return result;
}

/**
 * Console log helper to display role access results in a readable format
 * @param roles - Array of roles to test
 */
export function logRoleAccess(roles: string[]) {
  const result = testRoleAccess(roles);
  
  console.group(`🔐 Role Access Test: [${roles.join(', ')}]`);
  console.log(`📊 Total Groups: ${result.totalGroups}`);
  
  result.groups.forEach(group => {
    console.group(`📁 ${group.title || 'Main'} (${group.itemCount} items)`);
    
    group.items.forEach(item => {
      const icon = item.hasUrl ? '🔗' : item.hasSubItems ? '📂' : '📄';
      const roleInfo = item.requiredRoles.length > 0 ? ` [${item.requiredRoles.join(', ')}]` : ' [public]';
      
      console.log(`${icon} ${item.title}${roleInfo}`);
      
      if (item.subItems.length > 0) {
        item.subItems.forEach(subItem => {
          const subIcon = subItem.hasUrl ? '🔗' : subItem.hasSubItems ? '📂' : '📄';
          const subRoleInfo = subItem.requiredRoles.length > 0 ? ` [${subItem.requiredRoles.join(', ')}]` : ' [public]';
          console.log(`  ↳ ${subIcon} ${subItem.title}${subRoleInfo}`);
          
          if (subItem.hasSubItems) {
            console.log(`    └─ ${subItem.subItemCount} sub-items`);
          }
        });
      }
    });
    
    console.groupEnd();
  });
  
  console.groupEnd();
  return result;
}

/**
 * Test all predefined role combinations
 */
export function testAllRoles() {
  console.group('🧪 Testing All Role Combinations');
  
  Object.entries(TEST_ROLES).forEach(([roleName, roles]) => {
    logRoleAccess(roles);
  });
  
  console.groupEnd();
}

/**
 * Get a summary of what each role can access
 */
export function getRoleSummary() {
  const summary = Object.entries(TEST_ROLES).map(([roleName, roles]) => {
    const result = testRoleAccess(roles);
    const totalItems = result.groups.reduce((sum, group) => sum + group.itemCount, 0);
    const totalSubItems = result.groups.reduce((sum, group) => 
      sum + group.items.reduce((subSum, item) => subSum + item.subItemCount, 0), 0
    );
    
    return {
      roleName,
      roles,
      totalGroups: result.totalGroups,
      totalItems,
      totalSubItems,
      totalAccessibleItems: totalItems + totalSubItems
    };
  });
  
  return summary;
}

// Development helper - uncomment to test in browser console
// if (typeof window !== 'undefined') {
//   (window as any).testRoles = {
//     testRoleAccess,
//     logRoleAccess,
//     testAllRoles,
//     getRoleSummary,
//     TEST_ROLES
//   };
// }
